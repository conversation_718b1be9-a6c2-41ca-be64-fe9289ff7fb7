# 磁盘监控使用指南

## 概述

磁盘监控功能允许您监控系统的磁盘使用率，当磁盘使用率超过设定阈值时自动触发告警，并在使用率降低到安全水平时自动解决告警。

## 功能特性

- **实时监控**：定期检查磁盘使用率
- **多磁盘支持**：支持监控多个磁盘分区
- **灵活阈值**：可配置不同的告警阈值
- **自动告警**：超过阈值时自动触发告警
- **自动恢复**：使用率降低时自动解决告警
- **通知支持**：支持邮件和短信通知
- **历史数据**：存储磁盘使用率历史数据

## 架构说明

磁盘监控采用Agent-Server架构：

1. **Agent端**：部署在需要监控的服务器上，负责收集磁盘使用率数据
2. **Server端**：监控中心，定期从Agent获取数据，检查告警规则，发送通知

## 配置说明

### 1. Agent配置

Agent需要在目标服务器上运行，配置文件示例：

```yaml
# configs/agent/agent.yml
server:
  host: "0.0.0.0"
  port: 9090

security:
  token: "secure-token-123"

system:
  collect_interval: 10
```

### 2. Server配置

在Server端配置磁盘监控服务：

```yaml
# 服务配置
services:
  - name: "system-monitor-agent1"
    type: "system_resource"           # 系统资源监控类型
    check_interval: 60                # 检查间隔（秒）
    agent:
      host: "*************"           # Agent主机地址
      port: 9090                      # Agent端口
      token: "secure-token-123"       # Agent认证令牌

# 告警规则配置
alert_rules:
  - name: "high_disk_usage"
    services: ["system-monitor-agent1"]
    condition:
      type: "disk_usage"              # 告警类型
      threshold: 80                   # 阈值：80%
    severity: "warning"
    enabled: true

# 自动恢复配置
auto_resolve:
  enabled: true
  disk_recovery_threshold: 62         # 恢复阈值：80% * 62% = 49.6%
  send_notification: true
```

## 配置参数详解

### 服务配置参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `name` | 服务名称 | `"system-monitor-agent1"` |
| `type` | 服务类型，必须为 `system_resource` | `"system_resource"` |
| `check_interval` | 检查间隔（秒） | `60` |
| `agent.host` | Agent主机地址 | `"*************"` |
| `agent.port` | Agent端口 | `9090` |
| `agent.token` | Agent认证令牌 | `"secure-token-123"` |

### 告警规则参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `condition.type` | 告警类型，必须为 `disk_usage` | `"disk_usage"` |
| `condition.threshold` | 告警阈值（百分比） | `80` |
| `severity` | 告警级别：`info`、`warning`、`critical` | `"warning"` |

### 自动恢复参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `disk_recovery_threshold` | 恢复阈值百分比（相对于原阈值） | `62` |
| `send_notification` | 是否发送恢复通知 | `true` |

## 使用示例

### 基础配置

监控单台服务器，80%阈值告警，50%恢复：

```yaml
services:
  - name: "web-server-disk"
    type: "system_resource"
    check_interval: 60
    agent:
      host: "*************"
      port: 9090
      token: "web-server-token"

alert_rules:
  - name: "web_disk_usage"
    services: ["web-server-disk"]
    condition:
      type: "disk_usage"
      threshold: 80
    severity: "warning"
    enabled: true

auto_resolve:
  enabled: true
  disk_recovery_threshold: 62  # 80% * 62% = 49.6%
```

### 多级告警配置

配置警告和严重两级告警：

```yaml
alert_rules:
  # 警告级别：80%
  - name: "disk_warning"
    services: ["system-monitor-agent1"]
    condition:
      type: "disk_usage"
      threshold: 80
    severity: "warning"
    enabled: true

  # 严重级别：90%
  - name: "disk_critical"
    services: ["system-monitor-agent1"]
    condition:
      type: "disk_usage"
      threshold: 90
    severity: "critical"
    enabled: true
```

### 多服务器配置

监控多台服务器：

```yaml
services:
  - name: "web-server-1"
    type: "system_resource"
    check_interval: 60
    agent:
      host: "*************"
      port: 9090
      token: "token-1"

  - name: "db-server-1"
    type: "system_resource"
    check_interval: 30  # 数据库服务器更频繁检查
    agent:
      host: "*************"
      port: 9090
      token: "token-2"

alert_rules:
  # Web服务器使用80%阈值
  - name: "web_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 80
    severity: "warning"
    enabled: true

  # 数据库服务器使用更严格的75%阈值
  - name: "db_disk_usage"
    services: ["db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 75
    severity: "warning"
    enabled: true
```

## 告警流程

1. **监控检查**：Server定期向Agent请求磁盘使用率数据
2. **数据存储**：将磁盘使用率数据存储到指标历史表
3. **阈值检查**：检查每个磁盘分区的使用率是否超过配置阈值
4. **告警触发**：超过阈值时创建告警记录
5. **通知发送**：根据配置发送邮件/短信通知
6. **自动恢复**：使用率降低到恢复阈值时自动解决告警
7. **恢复通知**：发送告警恢复通知

## 监控数据

系统会为每个磁盘分区存储以下指标：

- **使用率百分比**：当前磁盘使用率
- **总容量**：磁盘总大小
- **已使用空间**：已使用的磁盘空间
- **可用空间**：剩余可用空间
- **挂载点**：磁盘挂载路径
- **设备名称**：磁盘设备标识

## 注意事项

1. **Agent部署**：确保Agent在目标服务器上正常运行
2. **网络连通性**：Server需要能够访问Agent的HTTP接口
3. **认证令牌**：确保Agent和Server配置相同的认证令牌
4. **阈值设置**：合理设置告警和恢复阈值，避免频繁告警
5. **检查间隔**：根据业务需求设置合适的检查间隔
6. **通知配置**：确保邮件和短信通知渠道配置正确

## 故障排查

### 常见问题

1. **无法获取磁盘数据**
   - 检查Agent是否运行
   - 检查网络连通性
   - 验证认证令牌

2. **告警未触发**
   - 检查告警规则配置
   - 确认服务名称匹配
   - 查看日志输出

3. **自动恢复不工作**
   - 检查 `auto_resolve.enabled` 配置
   - 验证恢复阈值设置
   - 确认磁盘使用率确实降低

### 日志查看

查看相关日志信息：

```bash
# Server端日志
tail -f /var/log/monitor/server.log | grep -i disk

# Agent端日志
tail -f /var/log/monitor/agent.log | grep -i disk
```

## API接口

### 获取磁盘使用情况

```bash
# 直接从Agent获取
curl -H "Authorization: Bearer secure-token-123" \
     http://*************:9090/api/v1/system/disk-usage

# 通过Server获取
curl http://server:8080/api/v1/agents/*************/disk-usage
```

### 查看告警记录

```bash
# 获取活跃告警
curl http://server:8080/api/v1/alerts?status=firing

# 获取特定服务的告警
curl http://server:8080/api/v1/alerts?service=system-monitor-agent1
```
