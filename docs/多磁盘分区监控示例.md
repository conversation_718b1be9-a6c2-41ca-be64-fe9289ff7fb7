# 多磁盘分区监控示例

## 概述

本文档详细说明多磁盘分区监控的工作原理，包括独立告警、自动恢复等功能。

## 监控原理

### 1. 分区发现
Agent会自动发现所有挂载的磁盘分区，包括：
- Linux: `/`, `/home`, `/var`, `/tmp`, `/data` 等
- Windows: `C:\`, `D:\`, `E:\` 等驱动器

### 2. 独立监控
每个磁盘分区都被独立监控：
- 独立的使用率计算
- 独立的告警触发
- 独立的告警恢复
- 独立的通知发送

### 3. 告警标识
每个分区的告警都有唯一标识：
```
格式：{规则名称}-{挂载点}
示例：
- high_disk_usage-/
- high_disk_usage-/home
- high_disk_usage-/var/log
- critical_disk_usage-C:\
- critical_disk_usage-D:\
```

## 实际场景演示

### 场景1：Linux Web服务器

**服务器配置**：
```yaml
services:
  - name: "web-server-1"
    type: "system_resource"
    check_interval: 60
    agent:
      host: "*************"
      port: 9090
      token: "web-token-123"
```

**磁盘状态**：
```
分区        挂载点    总容量   已用    使用率   状态
/dev/sda1   /         50GB    35GB    70%     正常
/dev/sda2   /home     100GB   85GB    85%     警告
/dev/sda3   /var/log  20GB    18GB    90%     严重
/dev/sdb1   /data     500GB   400GB   80%     警告
```

**告警结果**：
1. **`high_disk_usage-/home`** - 警告告警
   - 挂载点：`/home`
   - 当前使用率：85%
   - 阈值：80%
   - 状态：触发告警

2. **`critical_disk_usage-/var/log`** - 严重告警
   - 挂载点：`/var/log`
   - 当前使用率：90%
   - 阈值：90%
   - 状态：触发告警

3. **`high_disk_usage-/data`** - 警告告警
   - 挂载点：`/data`
   - 当前使用率：80%
   - 阈值：80%
   - 状态：触发告警

4. **根分区 `/`** - 无告警
   - 使用率70%，未达到阈值

### 场景2：Windows数据库服务器

**服务器配置**：
```yaml
services:
  - name: "db-server-1"
    type: "system_resource"
    check_interval: 30  # 数据库服务器更频繁检查
    agent:
      host: "*************"
      port: 9090
      token: "db-token-456"
```

**磁盘状态**：
```
驱动器   总容量    已用     使用率   状态
C:\      100GB    75GB     75%     正常
D:\      500GB    420GB    84%     警告
E:\      1TB      950GB    95%     严重
F:\      2TB      1.6TB    80%     警告
```

**告警结果**：
1. **`high_disk_usage-D:\`** - 警告告警
2. **`critical_disk_usage-E:\`** - 严重告警
3. **`high_disk_usage-F:\`** - 警告告警

## 自动恢复演示

### 恢复场景
假设经过清理后，磁盘使用率发生变化：

**清理前**：
```
/home: 85% → 触发告警 (high_disk_usage-/home)
/data: 80% → 触发告警 (high_disk_usage-/data)
```

**清理后**：
```
/home: 45% → 自动恢复告警 (80% × 62% = 49.6%，45% < 49.6%)
/data: 78% → 告警继续 (78% > 49.6%)
```

**恢复通知内容**：
```
磁盘使用率告警已恢复
服务: web-server-1
挂载点: /home
当前使用率: 45.00%
恢复阈值: 49.60%
告警持续时间: 2小时15分钟
```

## 配置最佳实践

### 1. 分层阈值配置

```yaml
alert_rules:
  # 第一层：警告级别
  - name: "disk_warning"
    condition:
      type: "disk_usage"
      threshold: 80
    severity: "warning"

  # 第二层：严重级别
  - name: "disk_critical"
    condition:
      type: "disk_usage"
      threshold: 90
    severity: "critical"

  # 第三层：紧急级别
  - name: "disk_emergency"
    condition:
      type: "disk_usage"
      threshold: 95
    severity: "critical"
```

### 2. 不同服务器类型的差异化配置

```yaml
# 数据库服务器 - 严格监控
- name: "db_disk_usage"
  services: ["db-server-1", "db-server-2"]
  condition:
    type: "disk_usage"
    threshold: 75  # 更严格的阈值
  severity: "warning"

# Web服务器 - 标准监控
- name: "web_disk_usage"
  services: ["web-server-1", "web-server-2"]
  condition:
    type: "disk_usage"
    threshold: 80  # 标准阈值
  severity: "warning"

# 备份服务器 - 宽松监控
- name: "backup_disk_usage"
  services: ["backup-server-1"]
  condition:
    type: "disk_usage"
    threshold: 90  # 更宽松的阈值
  severity: "warning"
```

### 3. 恢复阈值配置

```yaml
auto_resolve:
  enabled: true
  disk_recovery_threshold: 62  # 全局恢复阈值
  send_notification: true

# 计算示例：
# 80%阈值 × 62% = 49.6% 恢复阈值
# 90%阈值 × 62% = 55.8% 恢复阈值
# 75%阈值 × 62% = 46.5% 恢复阈值
```

## 监控数据存储

每个磁盘分区的数据都会独立存储：

```sql
-- 示例数据
INSERT INTO metrics_history (service_name, metric_name, metric_value, collected_at, tags) VALUES
('web-server-1', 'disk_usage', 70.0, '2024-01-15 10:00:00', '{"mountpoint":"/","device":"/dev/sda1"}'),
('web-server-1', 'disk_usage', 85.0, '2024-01-15 10:00:00', '{"mountpoint":"/home","device":"/dev/sda2"}'),
('web-server-1', 'disk_usage', 90.0, '2024-01-15 10:00:00', '{"mountpoint":"/var/log","device":"/dev/sda3"}'),
('web-server-1', 'disk_usage', 80.0, '2024-01-15 10:00:00', '{"mountpoint":"/data","device":"/dev/sdb1"}');
```

## 通知示例

### 告警通知
```
【监控告警】
服务：web-server-1
级别：warning
消息：磁盘挂载点：/home，当前使用率 85.0% 超过阈值 80.0%
时间：2024-01-15 10:05:00
```

### 恢复通知
```
【告警恢复】
服务：web-server-1
挂载点：/home
当前使用率：45.00%
恢复阈值：49.60%
告警持续时间：2小时15分钟
```

## 优势总结

1. **精确监控**：每个分区独立监控，不会因为一个分区正常而忽略其他分区的问题
2. **灵活告警**：可以为不同类型的服务器设置不同的阈值
3. **独立恢复**：每个分区的告警独立恢复，不会相互影响
4. **详细信息**：告警消息包含具体的挂载点和设备信息
5. **历史追踪**：每个分区的使用率历史都被独立记录
6. **扩展性强**：新增磁盘分区会自动被发现和监控

这种设计确保了磁盘监控的全面性和准确性，避免了传统监控中可能出现的盲点。
