集团接口平台规范V1.0

平台地址：[http://sms.ceic.com](http://sms.ceic.com:8011)

**http接口地址**

下行地址：<http://sms.ceic.com:8011/http/SendSms>

状态报告地址：<http://sms.ceic.com:8011/http/GetReport>

## 短信下行

### Http下行接口

功能：下发单条或者多条内容相同的信息

请求地址：<http://sms.ceic.com:8011/http/SendSms>

请求方式：get/post，推荐调用方式：post

请求数据类型：application/x-www-form-urlencoded

响应数据类型：text/xml

入参列表

| 参数名称 | 描述  | 字段值 |
| --- | --- | --- |
| Account | 账号  | String，不空 |
| Password | 密码  | String，不空（32位MD5加密） |
| Phone | 手机号码 | String,不空，支持多号码，号码之间用英文逗号隔开，最多100个 |
| Content | 短信内容 | String，不空，url_encode GBK编码 |
| SendTime | 定时发送时间 | String，可以为空，为空就是立即发送，格式是MM-dd-yyyy HH:mm:ss，例如：06-04-2018 16:55:01 |

返回参数列表

| 参数名称 | 描述  |
| --- | --- |
| response | 返回值状态编码，详见备注下行接口返回值状态码表 |
| sms |     |
| phone | 手机号 |
| smsID | 短信ID |

请求示例（get）：

<http://sms.ceic.com:8011/http/SendSms?Account=test&Password=b9086c5af65dc0f42bcc61e7dd89a624&Phone=***********&Content=123&SendTime=>

响应示例（xml格式）：

&lt;?xml version="1.0" encoding="UTF-8"?&gt;

&lt;result&gt;

&lt;response&gt;1&lt;/response&gt;

&lt;sms&gt;

&lt;phone&gt;***********,&lt;/phone&gt;

&lt;smsID&gt;230628143448010001&lt;/smsID&gt;

&lt;/sms&gt;

&lt;/result&gt;

## 短信状态

### Http状态查询接口

功能：通过批次id获取状态报告

请求地址：<http://sms.ceic.com:8011/http/GetReport>

请求方式：get/post，推荐调用方式：post

请求数据类型：application/x-www-form-urlencoded

响应数据类型：text/html

功能：查询状态报告

入参列表

| 参数名称 | 描述  | 字段值 |
| --- | --- | --- |
| Account | 账号  | String, 不空 |
| Password | 密码  | String，不空（32位MD5加密） |
| SmsId | 批次号，对应下行中响应返回的smsID | String，不空 |

返回参数列表

| 参数名称 | 描述  |
| --- | --- |
| response | 返回值状态编码，详见备注下行接口返回值状态码表 |
| sms |     |
| phone | 手机号 |
| smsID | 短信ID |
| stat | 状态码，未知状态为字符串null |

请求示例（get）：

[http://sms.ceic.com:8011/http/GetReport?Account=test&Password=b9086c5af65dc0f42bcc61e7dd89a624&SmsId=230628143448010001](http://sms.ceic.com:8011/http/GetReport?Account=admin&Password=b9086c5af65dc0f42bcc61e7dd89a6c7&SmsID=230628143448010001)

响应示例（xml格式）：

&lt;?xml version="1.0" encoding="UTF-8"?&gt;

&lt;result&gt;

&lt;response&gt;1&lt;/response&gt;

&lt;sms&gt;

&lt;smsID&gt;230628143448010001&lt;/smsID&gt;

&lt;phone&gt;***********&lt;/phone&gt;

&lt;stat&gt;DELIVRD&lt;/stat&gt;

&lt;/sms&gt;

&lt;/result&gt;

## 备注

### 下行接口返回值状态码表

| 状态编码 | 状态描述 |
| --- | --- |
| \>0 | 成功条数 |
| \-1 | 账号不存在，请检查用户名或密码是否正确 |
| \-2 | 账户余额不足 |
| \-3 | 账号已经被禁用 |
| \-4 | Ip鉴权失败 |
| \-8 | 缺少请求参数或参数名称不正确 |
| \-9 | 内容不合法 |
| \-10 | 账户当日发送量已经超过允许发送的数量 |
| \-11 | 通道未配置 |
| \-12 | 超过单次发送上限 |
| \-13 | 流速超速 |
| \-14 | 服务内部错误 |

### 状态报告接口返回值状态码表

| 状态编码 | 状态描述 |
| --- | --- |
| \>0 | 成功条数 |
| \-1 | 账号不存在，请检查用户名或密码是否正确 |
| \-3 | 账号已经被禁用 |
| \-4 | Ip鉴权失败 |
| \-8 | 缺少请求参数或参数名称不正确 |
| \-14 | 服务内部错误 |