# 分区独立配置设计方案

## 问题分析

当前磁盘监控的限制：
- 一个告警规则应用到所有磁盘分区
- 无法为不同分区设置不同阈值
- 无法排除某些分区（如临时分区）

## 改进方案

### 方案1：扩展现有配置结构

#### 配置示例
```yaml
alert_rules:
  # 系统分区严格监控
  - name: "system_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 75
      partition_filter:
        include: ["/", "/boot"]        # 只监控这些分区
        exclude: []                    # 排除的分区
    severity: "critical"

  # 用户数据分区标准监控
  - name: "data_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 80
      partition_filter:
        include: ["/home", "/data"]    # 只监控这些分区
        exclude: []
    severity: "warning"

  # 日志分区宽松监控
  - name: "log_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 90
      partition_filter:
        include: ["/var/log", "/tmp"]  # 只监控这些分区
        exclude: []
    severity: "warning"

  # 排除临时分区
  - name: "general_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 85
      partition_filter:
        include: []                    # 空表示所有分区
        exclude: ["/tmp", "/dev/shm"]  # 排除临时分区
    severity: "warning"
```

### 方案2：分区级别详细配置

#### 配置示例
```yaml
alert_rules:
  - name: "detailed_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      partitions:                      # 分区级别配置
        - mountpoint: "/"
          threshold: 75                # 根分区75%
          severity: "critical"
        - mountpoint: "/home"
          threshold: 80                # 用户目录80%
          severity: "warning"
        - mountpoint: "/var/log"
          threshold: 90                # 日志目录90%
          severity: "warning"
        - mountpoint: "/data"
          threshold: 85                # 数据目录85%
          severity: "critical"
        - device_pattern: "/dev/sd*"   # 支持设备模式匹配
          threshold: 80
          severity: "warning"
```

### 方案3：分区组配置

#### 配置示例
```yaml
# 定义分区组
partition_groups:
  system_partitions:
    - "/"
    - "/boot"
    - "/usr"
  
  data_partitions:
    - "/home"
    - "/data"
    - "/var/www"
  
  log_partitions:
    - "/var/log"
    - "/tmp"

alert_rules:
  # 系统分区组
  - name: "system_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 75
      partition_group: "system_partitions"  # 引用分区组
    severity: "critical"

  # 数据分区组
  - name: "data_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 80
      partition_group: "data_partitions"
    severity: "warning"
```

## 实现复杂度对比

| 方案 | 实现复杂度 | 配置复杂度 | 灵活性 | 推荐度 |
|------|------------|------------|--------|--------|
| 方案1 | 低 | 低 | 中 | ⭐⭐⭐⭐ |
| 方案2 | 中 | 高 | 高 | ⭐⭐⭐ |
| 方案3 | 高 | 中 | 高 | ⭐⭐ |

## 推荐实现：方案1

### 优势
1. **实现简单**：只需扩展现有配置结构
2. **向后兼容**：不影响现有配置
3. **易于理解**：配置逻辑清晰
4. **满足需求**：覆盖大部分使用场景

### 配置结构扩展

```go
// 在 AlertRuleConfig 中添加
type DiskUsageCondition struct {
    Type            string           `yaml:"type"`
    Threshold       interface{}      `yaml:"threshold"`
    PartitionFilter *PartitionFilter `yaml:"partition_filter,omitempty"`
}

type PartitionFilter struct {
    Include []string `yaml:"include,omitempty"` // 包含的分区
    Exclude []string `yaml:"exclude,omitempty"` // 排除的分区
}
```

### 实际使用示例

```yaml
# 生产环境配置示例
alert_rules:
  # 关键系统分区 - 严格监控
  - name: "critical_system_disk"
    services: ["web-server-1", "db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 70
      partition_filter:
        include: ["/", "/boot"]
    severity: "critical"

  # 应用数据分区 - 标准监控
  - name: "application_data_disk"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 80
      partition_filter:
        include: ["/var/www", "/data"]
    severity: "warning"

  # 用户数据分区 - 标准监控
  - name: "user_data_disk"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 85
      partition_filter:
        include: ["/home"]
    severity: "warning"

  # 日志分区 - 宽松监控
  - name: "log_disk"
    services: ["web-server-1", "db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 90
      partition_filter:
        include: ["/var/log"]
    severity: "warning"

  # 临时分区 - 非常宽松
  - name: "temp_disk"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 95
      partition_filter:
        include: ["/tmp", "/var/tmp"]
    severity: "info"

  # 数据库专用分区 - 严格监控
  - name: "database_disk"
    services: ["db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 75
      partition_filter:
        include: ["/var/lib/mysql", "/var/lib/postgresql"]
    severity: "critical"
```

## 实现效果

### 监控结果示例
```
服务器：web-server-1
分区状态：
├── / (75GB/100GB, 75%) → critical_system_disk 告警 (阈值70%)
├── /home (80GB/100GB, 80%) → 无告警 (阈值85%)
├── /var/log (18GB/20GB, 90%) → log_disk 告警 (阈值90%)
├── /data (85GB/100GB, 85%) → application_data_disk 告警 (阈值80%)
└── /tmp (19GB/20GB, 95%) → temp_disk 告警 (阈值95%)

告警列表：
1. critical_system_disk-/ (严重)
2. log_disk-/var/log (警告)  
3. application_data_disk-/data (警告)
4. temp_disk-/tmp (信息)
```

## 总结

当前实现确实不支持分区级别的独立配置，但通过扩展配置结构，可以实现：

1. **分区过滤**：指定监控哪些分区
2. **分区排除**：排除不需要监控的分区
3. **差异化阈值**：不同类型分区使用不同阈值
4. **灵活组合**：一个服务器可以有多个针对不同分区的规则

这样既保持了配置的简洁性，又提供了足够的灵活性来满足复杂的监控需求。
