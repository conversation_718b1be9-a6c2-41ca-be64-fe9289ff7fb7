# 发送简单邮件

> URL: http://*************:11003/email-endpoint/send-simple
>
> Origin Url: http://*************:11003/email-endpoint/send-simple
>
> Type: POST


### Request headers

|Header Name| Header Value|
|---------|------|

### Parameters

##### Path parameters

| Parameter | Type | Value | Description |
|---------|------|------|------------|


##### URL parameters

|Required| Parameter | Type | Value | Description |
|---------|---------|------|------|------------|


##### Body parameters

###### JSON

```

```

###### JSON document

```
null
```


##### Form URL-Encoded
|Required| Parameter | Type | Value | Description |
|---------|---------|------|------|------------|
|true|to|String|<EMAIL>|收件人邮箱|
|true|subject|String|subject_v7ucw|邮件主题|
|true|content|String|content_14ea7|邮件内容|


##### Multipart
|Required | Parameter | Type | Value | Description |
|---------|---------|------|------|------------|


### Response

##### Response example

```
{
  "code": 200,
  "success": true,
  "data": {},
  "msg": "邮件发送成功"
}
```

##### Response document
```
null
```


