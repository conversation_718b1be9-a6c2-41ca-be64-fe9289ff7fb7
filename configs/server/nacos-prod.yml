# 待确认
monitor:
  database:
    host: ***************
    port: 5422
    username: bdswzpg
    password: -W_zRGQsp6kqC9He^VjR
    database: bdswz
    schema: monitor
    max_connections: 20

  # 日志配置
  logging:
    level: "info"
    format: "console"



  # 全局Agent默认配置（所有使用Agent的服务共享）
  agent_defaults:
    port: 9090                             # 统一Agent端口
    token: "secure-token-123"       # 统一Agent认证token
    timeout: 30                            # 连接超时时间
    retry_count: 3                         # 重试次数

  # 全局监控默认配置（所有服务共享）
  monitor_defaults:
    type: "http"                           # 默认监控类型
    method: "GET"                          # 默认HTTP方法
    check_interval: 30                     # 默认检查间隔（秒）
    timeout: 10                            # 默认超时时间（秒）
    retry_count: 3                         # 默认重试次数
    expected_status: [200]            # 默认期望状态码
    health_path: "/health/check"        # 默认健康检查路径
    docker:                                # 默认Docker配置
      restart_policy: "unless-stopped"
      max_restart_count: 5
      restart_cooldown: "5m"

  # 简化的服务配置
  services:
    # Nacos 和 Docker 名称完全一致
    - name: "blade-gateway"
      docker:
        container_name: "blade-gateway"
    - name: "blade-auth"
      docker:
        container_name: "blade-auth"
    - name: "blade-system"
      docker:
        container_name: "blade-system"
    - name: "blade-log"
      docker:
        container_name: "blade-log"
    - name: "blade-resource"
      docker:
        container_name: "blade-resource"
    - name: "blade-user"
      docker:
        container_name: "blade-user"
    - name: "vdm-big-data"
      docker:
        container_name: "vdm-big-data"
    - name: "vdm-third-platform"
      docker:
        container_name: "vdm-third-platform"
    - name: "vdm-statistic"
      docker:
        container_name: "vdm-statistic"
    - name: "vdm-alarm"
      docker:
        container_name: "vdm-alarm"
    - name: "vdm-mqtt-check"
      docker:
        container_name: "vdm-mqtt-check"
    - name: "vdm-inter-auth"
      docker:
        container_name: "vdm-inter-auth"
    - name: "vdm-mqtt-official"
      docker:
        container_name: "vdm-mqtt-official"
    - name: "vdm-websocket"
      docker:
        container_name: "vdm-websocket"

#    - name: "vdm-inter-manager"
#      docker:
#        container_name: "vdm-inter-manager"
    - name: "vdm-bd-check"
      docker:
        container_name: "vdm-bd-check"
    - name: "vdm-base-info"
      docker:
        container_name: "vdm-base-info"
    # 待确认
    # Go 服务 (Nacos: go-xxx -> Docker: vdm-xxx)
    - name: "go-security"
      docker:
        container_name: "vdm-security"
    - name: "go-regulatorycenter"
      docker:
        container_name: "vdm-regulatorycenter"
    - name: "go-terminalcheck"
      docker:
        container_name: "vdm-terminalcheck"
    - name: "go-monitorcars"
      docker:
        container_name: "vdm-monitorcars"
    - name: "go-baseinfo"
      docker:
        container_name: "vdm-baseinfo"
    - name: "go-miniodfs"
      docker:
        container_name: "vdm-miniodfs"
    # 待确认
    # Wrapper 服务 (Nacos: xxx-wrapper -> Docker: vdm-wrapper-xxx)
    - name: "security-wrapper"
      docker:
        container_name: "vdm-wrapper-security"
    - name: "baseinfo-wrapper"
      docker:
        container_name: "vdm-wrapper-baseinfo"
    - name: "terminalcheck-wrapper"
      docker:
        container_name: "vdm-wrapper-terminalcheck"
    - name: "monitorcars-wrapper"
      docker:
        container_name: "vdm-wrapper-monitorcars"
    - name: "regulatorycenter-wrapper"
      docker:
        container_name: "vdm-wrapper-regulatorycenter"

    # 特殊匹配
    - name: "impala-gn-kafka"
      docker:
        container_name: "vdm-impala-gn-kafka"

    - name: "kafka"
      type: "kafka"
      check_interval: 30
      kafka:
        brokers:
          - "***************:9092"
          - "***************:9092"
          - "**************:9092"
          - "**************:9092"
        consumer_groups:
          - "location_send_group_prod_6"
        version: "2.2.1"
        timeout: 30
        retry_count: 3

    # EMQX测试监控服务
    - name: "emqx"
      type: "emqx"
      check_interval: 30
      emqx:
        host: "***************"
        port: 20123
        topics:
          - "send/RA0002/1859521080621895681"      # 测试设备数据主题
        rate_window: 300
        baseline_window: 900
        min_baseline_rate: 1          # 最小基线速率保护
        bucket_interval: 1    # 1秒桶，平衡精度和性能
        auth:
          username: "gn_connect"
          password: "ConnGN24@20"
        timeout: 30
        retry_count: 3
      docker:
        container_name: "vdm-inter-manager"
        agent:
          host: "*************"


  # 告警规则配置
  alert_rules:
    - name: "service-down"
      services: ["blade-gateway","blade-auth","blade-system","blade-log","blade-resource","blade-user","vdm-big-data","vdm-third-platform","vdm-statistic","vdm-alarm","vdm-mqtt-check","vdm-inter-auth","vdm-mqtt-official","vdm-websocket","vdm-inter-manager","vdm-bd-check","vdm-base-info","go-security","go-regulatorycenter","go-terminalcheck","go-monitorcars","go-baseinfo","go-miniodfs","security-wrapper","baseinfo-wrapper","terminalcheck-wrapper","monitorcars-wrapper","regulatorycenter-wrapper","impala-gn-kafka"]
      condition:
        type: "consecutive_failures"
        threshold: 3
      severity: "critical"
      enabled: true

    # Kafka主题消费延迟告警规则（支持容器重启）
    - name: "kafka_topic_lag_high"
      services: ["kafka"]
      condition:
        type: "topic_lag"
        threshold: 5000
      severity: "warning"
      enabled: true

    # EMQX速率下降告警
    - name: "emqx_rate_drop"
      services: ["emqx"]
      condition:
        type: "emqx_rate_drop"
        threshold: 10
      severity: "critical"
      enabled: true
      auto_restart_container: true

  # 通知配置
  notifications:
    enable_sms: true  # 启用短信通知
    # 通知渠道配置
    channels:
      email:
        # HTTP邮件接口配置（新增）
        url: "http://192.168.1.105:11003/email-endpoint/send-simple"
        timeout_seconds: 30
      sms:
        url: "http://sms.ceic.com:8011/http/SendSms"  # 短信接口请求地址
        username: "SZJCYYFWPT"  # 短信平台账号
        password: "0248b7fdf7e546e6e59ab6008ba4988d"  # 短信平台密码（MD5加密）
        timeout_seconds: 30  # 请求超时时间（秒）


    # 通知分组和策略
    groups:
      - name: "backend-team"
        services: ["kafka", "emqx", "blade-gateway","blade-auth","blade-system","blade-log","blade-resource","blade-user","vdm-big-data","vdm-third-platform","vdm-statistic","vdm-alarm","vdm-mqtt-check","vdm-inter-auth","vdm-mqtt-official","vdm-websocket","vdm-inter-manager","vdm-bd-check","vdm-base-info","go-security","go-regulatorycenter","go-terminalcheck","go-monitorcars","go-baseinfo","go-miniodfs","security-wrapper","baseinfo-wrapper","terminalcheck-wrapper","monitorcars-wrapper","regulatorycenter-wrapper","impala-gn-kafka"]
        channels: ["sms"]
        recipients:
          sms: ["15010018049"]



  # 告警自动恢复配置
  auto_resolve:
    enabled: true                     # 启用告警自动恢复
    healthy_checks_required: 2        # HTTP服务需要连续2次健康检查通过才自动恢复告警
    kafka_recovery_threshold: 50     # Kafka延迟低于阈值的80%时自动恢复告警
    emqx_recovery_threshold: 50      # 消费速率大于基线的80%时自动恢复告警
    send_notification: false          # 不发送恢复通知