# 磁盘监控配置示例
# 此文件展示如何配置磁盘使用率监控和告警

# 服务配置
services:
  # 系统资源监控服务示例
  - name: "system-monitor-agent1"
    type: "system_resource"           # 系统资源监控类型
    check_interval: 60                # 检查间隔（秒）
    timeout: 30                       # 超时时间（秒）
    retry_count: 3                    # 重试次数
    agent:                            # Agent配置
      host: "*************"           # Agent主机地址
      port: 9090                      # Agent端口
      token: "secure-token-123"       # Agent认证令牌

  - name: "system-monitor-agent2"
    type: "system_resource"
    check_interval: 120               # 2分钟检查一次
    timeout: 30
    retry_count: 3
    agent:
      host: "*************"
      port: 9090
      token: "secure-token-456"

# 告警规则配置
alert_rules:
  # 通用磁盘使用率告警规则（适用于所有分区）
  - name: "high_disk_usage"
    services: ["system-monitor-agent1", "system-monitor-agent2"]
    condition:
      type: "disk_usage"              # 告警类型：磁盘使用率
      threshold: 80                   # 磁盘使用率超过80%触发告警
    severity: "warning"               # 告警级别：警告
    enabled: true                     # 启用告警
    # 注意：此规则会监控所有磁盘分区，每个分区独立告警

  # 严重磁盘使用率告警规则（适用于所有分区）
  - name: "critical_disk_usage"
    services: ["system-monitor-agent1", "system-monitor-agent2"]
    condition:
      type: "disk_usage"
      threshold: 90                   # 磁盘使用率超过90%触发严重告警
    severity: "critical"              # 告警级别：严重
    enabled: true
    # 注意：每个分区达到90%都会独立触发严重告警

  # 针对数据库服务器的严格监控
  - name: "database_disk_usage"
    services: ["system-monitor-agent1"]  # 只监控数据库服务器
    condition:
      type: "disk_usage"
      threshold: 75                   # 数据库服务器使用更严格的阈值
    severity: "warning"
    enabled: true
    # 数据库服务器的所有分区都使用75%的严格阈值

  # 系统关键分区监控（如果需要特殊处理某些分区）
  - name: "system_partition_usage"
    services: ["system-monitor-agent1", "system-monitor-agent2"]
    condition:
      type: "disk_usage"
      threshold: 85                   # 系统分区使用85%阈值
    severity: "critical"
    enabled: true
    # 注意：这会对所有分区生效，包括根分区、日志分区等

# 自动恢复配置
auto_resolve:
  enabled: true                       # 启用自动恢复
  disk_recovery_threshold: 62         # 磁盘使用率降到原阈值的62%时自动解决告警
                                      # 例如：80%阈值 * 62% = 49.6%，当使用率降到49.6%以下时自动解决
  send_notification: true             # 发送恢复通知

# 通知配置
notifications:
  channels:
    email:
      url: "http://*************:11003/email-endpoint/send-simple"
      timeout_seconds: 30
    sms:
      url: "http://sms.ceic.com:8011/http/SendSms"
      username: "SZJCYYFWPT"
      password: "0248b7fdf7e546e6e59ab6008ba4988d"
      timeout_seconds: 30

  # 通知分组
  groups:
    - name: "disk_alerts"
      description: "磁盘告警通知组"
      severity_filter: ["warning", "critical"]
      channels: ["email", "sms"]
      recipients:
        email: ["<EMAIL>", "<EMAIL>"]
        sms: ["13800138000", "13900139000"]

# 数据库配置
database:
  host: "localhost"
  port: 5432
  username: "monitor_user"
  password: "monitor_password"
  database: "monitor_db"
  ssl_mode: "disable"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080

# 日志配置
logging:
  level: "info"
  format: "json"
