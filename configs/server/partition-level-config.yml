# 分区级别磁盘监控配置示例
# 支持为不同分区设置不同的阈值和严重级别

# 服务配置
services:
  # Web服务器
  - name: "web-server-1"
    type: "system_resource"
    check_interval: 60
    agent:
      host: "*************"
      port: 9090
      token: "web-token-001"

  # 数据库服务器
  - name: "db-server-1"
    type: "system_resource"
    check_interval: 30
    agent:
      host: "*************"
      port: 9090
      token: "db-token-001"

# 告警规则配置
alert_rules:
  # ==================== Web服务器分区级别监控 ====================
  
  - name: "web_detailed_disk_usage"
    services: ["web-server-1"]
    condition:
      type: "disk_usage"
      threshold: 80                    # 🎯 默认阈值：80%（未配置的分区使用此阈值）
      partitions:                      # 🔧 分区级别配置（覆盖默认值）
        - mountpoint: "/"
          threshold: 75                # 根分区75%（更严格）
          severity: "critical"         # 根分区问题更严重
        - mountpoint: "/boot"
          threshold: 70                # 启动分区70%（最严格）
          severity: "critical"
        - mountpoint: "/var/log"
          threshold: 90                # 日志分区90%（更宽松）
          severity: "warning"
        - mountpoint: "/tmp"
          threshold: 95                # 临时分区95%（最宽松）
          severity: "info"
        # /home, /data 等其他分区使用默认的80%阈值和warning级别
    severity: "warning"                # 默认严重级别
    enabled: true

  # ==================== 数据库服务器分区级别监控 ====================
  
  - name: "db_detailed_disk_usage"
    services: ["db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 75                    # 🎯 默认阈值：75%（数据库服务器更严格）
      partitions:                      # 🔧 分区级别配置
        - mountpoint: "/"
          threshold: 70                # 根分区70%
          severity: "critical"
        - mountpoint: "/var/lib/mysql"
          threshold: 80                # MySQL数据目录80%
          severity: "critical"         # 数据库数据非常重要
        - mountpoint: "/var/lib/postgresql"
          threshold: 80                # PostgreSQL数据目录80%
          severity: "critical"
        - mountpoint: "/var/log"
          threshold: 85                # 数据库日志85%
          severity: "warning"
        - device_pattern: "/dev/nvme*"  # 🎯 设备模式匹配：所有NVMe设备
          threshold: 85                # NVMe设备85%
          severity: "warning"
        # 其他分区使用默认的75%阈值
    severity: "warning"                # 默认严重级别
    enabled: true

  # ==================== 通用严重告警（所有服务器） ====================
  
  - name: "critical_disk_usage"
    services: ["web-server-1", "db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 95                    # 🎯 默认阈值：95%（紧急情况）
      partitions:                      # 🔧 关键分区更严格
        - mountpoint: "/"
          threshold: 90                # 根分区90%就是紧急情况
          severity: "critical"
        - mountpoint: "/boot"
          threshold: 85                # 启动分区85%就是紧急情况
          severity: "critical"
        # 其他分区使用默认的95%阈值
    severity: "critical"               # 默认严重级别
    enabled: true

# 自动恢复配置
auto_resolve:
  enabled: true
  disk_recovery_threshold: 62         # 🔄 恢复阈值：原阈值的62%
  send_notification: true

# 通知配置
notifications:
  channels:
    email:
      url: "http://mail-service:8080/send"
      timeout_seconds: 30
    sms:
      url: "http://sms-service:8080/send"
      timeout_seconds: 30

  groups:
    # Web团队通知组
    - name: "web_team"
      description: "Web服务器团队"
      severity_filter: ["warning", "critical"]
      services: ["web-server-1"]
      channels: ["email"]
      recipients:
        email: ["<EMAIL>"]

    # 数据库团队通知组
    - name: "db_team"
      description: "数据库团队"
      severity_filter: ["warning", "critical"]
      services: ["db-server-1"]
      channels: ["email", "sms"]
      recipients:
        email: ["<EMAIL>"]
        sms: ["13800138000"]

    # 紧急通知组
    - name: "emergency_team"
      description: "紧急响应团队"
      severity_filter: ["critical"]
      services: ["web-server-1", "db-server-1"]
      channels: ["sms"]
      recipients:
        sms: ["13900139000", "13800138000"]

# 数据库配置
database:
  host: "localhost"
  port: 5432
  username: "monitor_user"
  password: "monitor_password"
  database: "monitor_db"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080

# 日志配置
logging:
  level: "info"
  format: "json"
