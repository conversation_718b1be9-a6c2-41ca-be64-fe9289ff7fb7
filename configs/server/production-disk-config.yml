# 生产环境磁盘监控配置示例
# 此文件展示如何在生产环境中配置磁盘监控阈值

# 服务配置
services:
  # Web服务器集群
  - name: "web-server-1"
    type: "system_resource"
    check_interval: 60
    agent:
      host: "*************"
      port: 9090
      token: "web-token-001"

  - name: "web-server-2"
    type: "system_resource"
    check_interval: 60
    agent:
      host: "*************"
      port: 9090
      token: "web-token-002"

  # 数据库服务器
  - name: "db-server-1"
    type: "system_resource"
    check_interval: 30  # 数据库服务器更频繁检查
    agent:
      host: "*************"
      port: 9090
      token: "db-token-001"

  # 备份服务器
  - name: "backup-server-1"
    type: "system_resource"
    check_interval: 300  # 备份服务器检查频率较低
    agent:
      host: "192.168.1.300"
      port: 9090
      token: "backup-token-001"

# 告警规则配置 - 这里定义所有的阈值
alert_rules:
  # ==================== Web服务器磁盘监控 ====================
  
  # Web服务器警告级别：80%阈值
  - name: "web_disk_warning"
    services: ["web-server-1", "web-server-2"]
    condition:
      type: "disk_usage"
      threshold: 80        # 🎯 Web服务器80%警告阈值
    severity: "warning"
    enabled: true

  # Web服务器严重级别：90%阈值
  - name: "web_disk_critical"
    services: ["web-server-1", "web-server-2"]
    condition:
      type: "disk_usage"
      threshold: 90        # 🎯 Web服务器90%严重阈值
    severity: "critical"
    enabled: true

  # ==================== 数据库服务器磁盘监控 ====================
  
  # 数据库服务器警告级别：75%阈值（更严格）
  - name: "db_disk_warning"
    services: ["db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 75        # 🎯 数据库服务器75%警告阈值
    severity: "warning"
    enabled: true

  # 数据库服务器严重级别：85%阈值（更严格）
  - name: "db_disk_critical"
    services: ["db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 85        # 🎯 数据库服务器85%严重阈值
    severity: "critical"
    enabled: true

  # 数据库服务器紧急级别：95%阈值
  - name: "db_disk_emergency"
    services: ["db-server-1"]
    condition:
      type: "disk_usage"
      threshold: 95        # 🎯 数据库服务器95%紧急阈值
    severity: "critical"
    enabled: true

  # ==================== 备份服务器磁盘监控 ====================
  
  # 备份服务器警告级别：90%阈值（较宽松）
  - name: "backup_disk_warning"
    services: ["backup-server-1"]
    condition:
      type: "disk_usage"
      threshold: 90        # 🎯 备份服务器90%警告阈值
    severity: "warning"
    enabled: true

  # 备份服务器严重级别：95%阈值
  - name: "backup_disk_critical"
    services: ["backup-server-1"]
    condition:
      type: "disk_usage"
      threshold: 95        # 🎯 备份服务器95%严重阈值
    severity: "critical"
    enabled: true

# 自动恢复配置
auto_resolve:
  enabled: true
  disk_recovery_threshold: 62    # 🔄 恢复阈值：原阈值的62%
  send_notification: true

# 通知配置
notifications:
  channels:
    email:
      url: "http://mail-service:8080/send"
      timeout_seconds: 30
    sms:
      url: "http://sms-service:8080/send"
      timeout_seconds: 30

  groups:
    # Web服务器通知组
    - name: "web_team"
      description: "Web服务器团队"
      severity_filter: ["warning", "critical"]  # 只接收警告和严重级别
      services: ["web-server-1", "web-server-2"]
      channels: ["email"]
      recipients:
        email: ["<EMAIL>", "<EMAIL>"]

    # 数据库团队通知组
    - name: "db_team"
      description: "数据库团队"
      severity_filter: ["warning", "critical"]
      services: ["db-server-1"]
      channels: ["email", "sms"]  # 数据库问题同时发送邮件和短信
      recipients:
        email: ["<EMAIL>", "<EMAIL>"]
        sms: ["13800138000", "13900139000"]

    # 运维团队通知组（接收所有告警）
    - name: "ops_team"
      description: "运维团队"
      severity_filter: ["warning", "critical"]
      services: ["web-server-1", "web-server-2", "db-server-1", "backup-server-1"]
      channels: ["email"]
      recipients:
        email: ["<EMAIL>"]

# 数据库配置
database:
  host: "localhost"
  port: 5432
  username: "monitor_user"
  password: "monitor_password"
  database: "monitor_db"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080

# 日志配置
logging:
  level: "info"
  format: "json"
