package database

import (
	"fmt"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/model"
	"monitor/internal/shared/logger"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// DB GORM数据库连接实例
var DB *gorm.DB

// Init 初始化GORM数据库连接
func Init(cfg *config.DatabaseConfig) error {
	// 构建DSN，包含schema设置
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.Database, cfg.SSLMode)

	// 如果指定了schema，添加到DSN中
	if cfg.Schema != "" {
		dsn += fmt.Sprintf(" search_path=%s", cfg.Schema)
	}

	// GORM配置
	gormConfig := &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogger.Silent), // 关闭SQL日志
		NowFunc: func() time.Time {
			return time.Now()
		},
	}

	// 创建GORM连接
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层sql.DB以设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取底层sql.DB失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(cfg.MaxConns)
	sqlDB.SetMaxIdleConns(cfg.MaxConns / 2)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	DB = db
	logger.Info("GORM数据库连接已建立",
		"host", cfg.Host,
		"port", cfg.Port,
		"database", cfg.Database,
		"schema", cfg.Schema)

	return nil
}

// Close 关闭数据库连接
func Close() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// Ping 检查数据库连接
func Ping() error {
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// AutoMigrate 自动迁移数据库表结构（可选，用于开发环境）
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	// 自动迁移所有模型
	err := DB.AutoMigrate(
		&model.Alert{},
		&model.Notification{},
		&model.ServiceHealth{},
		&model.MetricsHistory{},
	)

	if err != nil {
		return fmt.Errorf("自动迁移失败: %w", err)
	}

	logger.Info("数据库自动迁移完成")
	return nil
}
