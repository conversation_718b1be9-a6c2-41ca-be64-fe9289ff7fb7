#!/bin/bash

# 监控Agent启动脚本 - 简化版
# 只负责启动二进制文件和watchdog

set -e

# 配置变量
CURRENT_DIR=$(pwd)
AGENT_HOME="$CURRENT_DIR"
LOG_DIR="$CURRENT_DIR/logs"
PID_FILE="$CURRENT_DIR/monitor-agent.pid"
WATCHDOG_PID_FILE="$CURRENT_DIR/monitor-watchdog.pid"
LOG_FILE="$LOG_DIR/monitor-agent.log"
WATCHDOG_LOG_FILE="$LOG_DIR/watchdog.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查二进制文件
check_binary() {
    log_info "检查二进制文件..."

    if [[ ! -f "monitor-agent" ]]; then
        log_error "未找到monitor-agent二进制文件"
        exit 1
    fi

    if [[ ! -x "monitor-agent" ]]; then
        chmod +x monitor-agent
    fi

    log_success "二进制文件检查通过"
}

# 创建日志目录
create_dirs() {
    log_info "创建日志目录..."
    mkdir -p "$LOG_DIR"
}

# 停止watchdog进程
stop_watchdog() {
    if [[ -f "$WATCHDOG_PID_FILE" ]]; then
        WATCHDOG_PID=$(cat "$WATCHDOG_PID_FILE")
        if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
            log_info "停止Watchdog进程 (PID: $WATCHDOG_PID)..."
            kill "$WATCHDOG_PID"
            sleep 2
            if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
                log_warn "强制停止Watchdog进程..."
                kill -9 "$WATCHDOG_PID"
            fi
        fi
        rm -f "$WATCHDOG_PID_FILE"
    fi
}

# 停止现有进程
stop_agent() {
    # 先停止watchdog
    stop_watchdog

    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_info "停止现有Agent进程 (PID: $PID)..."
            kill "$PID"
            sleep 2
            if kill -0 "$PID" 2>/dev/null; then
                log_warn "强制停止Agent进程..."
                kill -9 "$PID"
            fi
        fi
        rm -f "$PID_FILE"
    fi
}

# 启动watchdog守护进程
start_watchdog() {
    log_info "启动Watchdog守护进程..."

    # 确保watchdog脚本存在且可执行
    if [[ ! -f "./monitor-watchdog.sh" ]]; then
        log_error "未找到monitor-watchdog.sh脚本"
        return 1
    fi

    if [[ ! -x "./monitor-watchdog.sh" ]]; then
        chmod +x "./monitor-watchdog.sh"
    fi

    # 启动watchdog守护进程
    cd "$AGENT_HOME"
    nohup ./monitor-watchdog.sh daemon > "$WATCHDOG_LOG_FILE" 2>&1 &
    WATCHDOG_PID=$!
    echo $WATCHDOG_PID > "$WATCHDOG_PID_FILE"

    # 等待启动
    sleep 2

    if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
        log_success "Watchdog启动成功 (PID: $WATCHDOG_PID)"
    else
        log_error "Watchdog启动失败"
        if [[ -f "$WATCHDOG_LOG_FILE" ]]; then
            log_error "错误日志:"
            tail -10 "$WATCHDOG_LOG_FILE"
        fi
        return 1
    fi
}

# 启动Agent
start_agent() {
    log_info "启动Monitor Agent..."

    # 启动Agent进程
    cd "$AGENT_HOME"
    nohup ./monitor-agent > "$LOG_FILE" 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"

    # 等待启动
    sleep 3

    if kill -0 "$PID" 2>/dev/null; then
        log_success "Agent启动成功 (PID: $PID)"
        # 启动watchdog守护进程
        start_watchdog
    else
        log_error "Agent启动失败"
        if [[ -f "$LOG_FILE" ]]; then
            log_error "错误日志:"
            tail -10 "$LOG_FILE"
        fi
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    # 等待服务启动
    sleep 3

    # 检查进程状态
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_success "Agent进程运行正常 (PID: $PID)"
        else
            log_warn "Agent进程未运行"
            return 1
        fi
    else
        log_warn "未找到Agent PID文件"
        return 1
    fi

    # 检查Watchdog状态
    if [[ -f "$WATCHDOG_PID_FILE" ]]; then
        WATCHDOG_PID=$(cat "$WATCHDOG_PID_FILE")
        if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
            log_success "Watchdog进程运行正常 (PID: $WATCHDOG_PID)"
        else
            log_warn "Watchdog进程未运行"
        fi
    fi
}

# 显示状态
show_status() {
    echo ""
    echo "==================================="
    echo "Monitor Agent 状态"
    echo "==================================="

    # Agent状态
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            echo "Agent状态: 运行中 (PID: $PID)"
        else
            echo "Agent状态: 已停止 (PID文件存在但进程不存在)"
        fi
    else
        echo "Agent状态: 已停止"
    fi

    # Watchdog状态
    if [[ -f "$WATCHDOG_PID_FILE" ]]; then
        WATCHDOG_PID=$(cat "$WATCHDOG_PID_FILE")
        if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
            echo "Watchdog状态: 运行中 (PID: $WATCHDOG_PID)"
        else
            echo "Watchdog状态: 已停止 (PID文件存在但进程不存在)"
        fi
    else
        echo "Watchdog状态: 已停止"
    fi

    echo ""
    echo "工作目录: $AGENT_HOME"
    echo "Agent日志: $LOG_FILE"
    echo "Watchdog日志: $WATCHDOG_LOG_FILE"
    echo "Agent PID文件: $PID_FILE"
    echo "Watchdog PID文件: $WATCHDOG_PID_FILE"
    echo ""
    echo "管理命令:"
    echo "  启动: ./start-agent.sh start"
    echo "  停止: ./start-agent.sh stop"
    echo "  重启: ./start-agent.sh restart"
    echo "  状态: ./start-agent.sh status"
    echo "  Agent日志: tail -f $LOG_FILE"
    echo "  Watchdog日志: tail -f $WATCHDOG_LOG_FILE"
    echo "==================================="
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            log_info "启动Monitor Agent..."
            check_binary
            create_dirs
            stop_agent
            start_agent
            verify_deployment
            show_status
            ;;
        stop)
            log_info "停止Monitor Agent..."
            stop_agent
            log_success "Agent已停止"
            ;;
        restart)
            log_info "重启Monitor Agent..."
            stop_agent
            sleep 1
            check_binary
            create_dirs
            start_agent
            verify_deployment
            show_status
            ;;
        status)
            show_status
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status}"
            echo "默认操作: start"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
