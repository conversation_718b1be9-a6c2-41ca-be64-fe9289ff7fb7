#!/bin/bash

# Monitor Agent 看门狗脚本 - 简化版
# 只负责监控Agent进程并在停止时重启

set -e

# 配置变量
CURRENT_DIR=$(pwd)
AGENT_DIR="$CURRENT_DIR"
PID_FILE="$AGENT_DIR/monitor-agent.pid"
WATCHDOG_PID_FILE="$AGENT_DIR/monitor-watchdog.pid"
LOG_FILE="$AGENT_DIR/logs/watchdog.log"
CHECK_INTERVAL=30  # 检查间隔（秒）
MAX_RESTART_ATTEMPTS=5  # 最大重启尝试次数
RESTART_COOLDOWN=300  # 重启冷却时间（秒）

# 日志函数
log_message() {
    local message="$(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    # 如果不是守护进程模式，也输出到控制台
    if [[ "${DAEMON_MODE:-false}" != "true" ]]; then
        echo "$message"
    fi
}

log_info() {
    log_message "[INFO] $1"
}

log_warn() {
    log_message "[WARN] $1"
}

log_error() {
    log_message "[ERROR] $1"
}

log_success() {
    log_message "[SUCCESS] $1"
}

# 检查进程是否运行
check_process() {
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            return 0  # 进程运行中
        else
            return 1  # 进程不存在
        fi
    else
        return 1  # PID文件不存在
    fi
}

# 创建锁文件防止重复启动
create_lock() {
    local lock_file="$AGENT_DIR/watchdog.lock"
    if [[ -f "$lock_file" ]]; then
        local lock_pid=$(cat "$lock_file")
        if kill -0 "$lock_pid" 2>/dev/null; then
            log_error "另一个watchdog实例正在运行 (PID: $lock_pid)"
            return 1
        else
            rm -f "$lock_file"
        fi
    fi
    echo $$ > "$lock_file"
    return 0
}

# 清理锁文件
cleanup_lock() {
    local lock_file="$AGENT_DIR/watchdog.lock"
    rm -f "$lock_file"
}

# 信号处理函数
cleanup_and_exit() {
    log_info "收到退出信号，正在清理..."
    cleanup_lock
    exit 0
}

# 重启Agent
restart_agent() {
    log_warn "正在重启Monitor Agent..."
    cd "$AGENT_DIR"

    # 停止现有Agent进程
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_info "停止Agent进程 (PID: $PID)..."
            kill "$PID"
            sleep 3
            if kill -0 "$PID" 2>/dev/null; then
                log_warn "强制停止Agent进程..."
                kill -9 "$PID"
            fi
        fi
        rm -f "$PID_FILE"
    fi

    # 启动新的Agent进程
    log_info "启动新的Agent进程..."
    if [[ -f "./monitor-agent" ]]; then
        nohup ./monitor-agent > "$AGENT_DIR/logs/monitor-agent.log" 2>&1 &
        local new_pid=$!
        echo $new_pid > "$PID_FILE"

        # 等待启动
        sleep 5

        if kill -0 "$new_pid" 2>/dev/null; then
            log_success "Agent重启成功 (PID: $new_pid)"
            return 0
        else
            log_error "Agent重启失败"
            return 1
        fi
    else
        log_error "未找到monitor-agent二进制文件"
        return 1
    fi
}

# 单次检查逻辑
check_once() {
    # 检查进程
    if ! check_process; then
        log_warn "检测到问题: Agent进程未运行，正在重启..."
        if restart_agent; then
            log_success "Agent重启完成"
            return 0
        else
            log_error "Agent重启失败"
            return 1
        fi
    else
        log_info "Agent运行正常"
        return 0
    fi
}

# 守护进程模式
daemon_mode() {
    log_info "启动Watchdog守护进程模式..."
    log_info "检查间隔: ${CHECK_INTERVAL}秒"

    local restart_count=0
    local last_restart_time=0

    while true; do
        local current_time=$(date +%s)

        # 检查是否需要重置重启计数器
        if [[ $((current_time - last_restart_time)) -gt $RESTART_COOLDOWN ]]; then
            restart_count=0
        fi

        # 执行检查
        if ! check_once; then
            restart_count=$((restart_count + 1))
            last_restart_time=$current_time

            if [[ $restart_count -ge $MAX_RESTART_ATTEMPTS ]]; then
                log_error "达到最大重启尝试次数 ($MAX_RESTART_ATTEMPTS)，进入冷却期..."
                sleep $RESTART_COOLDOWN
                restart_count=0
            fi
        fi

        # 等待下次检查
        sleep $CHECK_INTERVAL
    done
}

# 显示状态
show_status() {
    echo "==================================="
    echo "Watchdog 状态"
    echo "==================================="

    if [[ -f "$WATCHDOG_PID_FILE" ]]; then
        WATCHDOG_PID=$(cat "$WATCHDOG_PID_FILE")
        if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
            echo "Watchdog状态: 运行中 (PID: $WATCHDOG_PID)"
        else
            echo "Watchdog状态: 已停止"
        fi
    else
        echo "Watchdog状态: 已停止"
    fi

    if [[ -f "$PID_FILE" ]]; then
        AGENT_PID=$(cat "$PID_FILE")
        if kill -0 "$AGENT_PID" 2>/dev/null; then
            echo "Agent状态: 运行中 (PID: $AGENT_PID)"
        else
            echo "Agent状态: 已停止"
        fi
    else
        echo "Agent状态: 已停止"
    fi

    echo ""
    echo "检查间隔: ${CHECK_INTERVAL}秒"
    echo "日志文件: $LOG_FILE"
    echo "==================================="
}

# 停止守护进程
stop_daemon() {
    if [[ -f "$WATCHDOG_PID_FILE" ]]; then
        WATCHDOG_PID=$(cat "$WATCHDOG_PID_FILE")
        if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
            echo "停止Watchdog守护进程 (PID: $WATCHDOG_PID)..."
            kill "$WATCHDOG_PID"
            sleep 2
            if kill -0 "$WATCHDOG_PID" 2>/dev/null; then
                echo "强制停止Watchdog进程..."
                kill -9 "$WATCHDOG_PID"
            fi
            rm -f "$WATCHDOG_PID_FILE"
            echo "Watchdog已停止"
        else
            echo "Watchdog进程不存在"
            rm -f "$WATCHDOG_PID_FILE"
        fi
    else
        echo "Watchdog未运行"
    fi
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"

    case "${1:-check}" in
        daemon)
            # 守护进程模式
            if ! create_lock; then
                exit 1
            fi

            # 设置信号处理
            trap cleanup_and_exit SIGTERM SIGINT

            # 设置守护进程模式标志
            export DAEMON_MODE=true

            # 记录PID
            echo $$ > "$WATCHDOG_PID_FILE"

            # 启动守护进程
            daemon_mode
            ;;
        check)
            # 单次检查模式
            check_once
            ;;
        status)
            show_status
            ;;
        stop)
            stop_daemon
            ;;
        *)
            echo "用法: $0 {daemon|check|status|stop}"
            echo "  daemon - 守护进程模式"
            echo "  check  - 单次检查模式"
            echo "  status - 显示状态"
            echo "  stop   - 停止守护进程"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
