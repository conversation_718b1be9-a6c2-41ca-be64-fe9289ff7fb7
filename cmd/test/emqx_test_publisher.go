package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// TestMessage 测试消息结构
type TestMessage struct {
	DeviceID  string    `json:"device_id"`
	Timestamp time.Time `json:"timestamp"`
	Location  Location  `json:"location"`
	Speed     float64   `json:"speed"`
	Status    string    `json:"status"`
	Battery   int       `json:"battery"`
}

// Location 位置信息
type Location struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Altitude  float64 `json:"altitude"`
}

// EMQXConfig EMQX连接配置
type EMQXConfig struct {
	Broker   string
	Port     int
	Username string
	Password string
	ClientID string
	Topic    string
}

func main() {
	// EMQX配置（根据你的配置调整）
	config := EMQXConfig{
		Broker:   "*************",
		Port:     1883,
		Username: "admin",
		Password: "emqX@2025",
		ClientID: "test_publisher_" + strconv.FormatInt(time.Now().Unix(), 10),
		Topic:    "send/RA0002/1859521080621895681",
	}

	fmt.Printf("EMQX测试数据发布器\n")
	fmt.Printf("目标主题: %s\n", config.Topic)
	fmt.Printf("EMQX服务器: %s:%d\n", config.Broker, config.Port)
	fmt.Printf("客户端ID: %s\n", config.ClientID)
	fmt.Println("按 Ctrl+C 停止发送")
	fmt.Println("=" + string(make([]byte, 50)) + "=")

	// 创建MQTT客户端
	client := createMQTTClient(config)
	if client == nil {
		log.Fatal("创建MQTT客户端失败")
	}
	defer client.Disconnect(250)

	// 连接到EMQX
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		log.Fatalf("Failed to connect to EMQX: %v", token.Error())
	}
	fmt.Println("✅ 成功连接到EMQX服务器")

	// 设置信号处理，优雅退出
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动数据发送
	go startPublishing(client, config.Topic, sigChan)

	// 等待退出信号
	<-sigChan
	fmt.Println("\n🛑 收到退出信号，正在停止...")
	fmt.Println("✅ 测试完成")
}

// createMQTTClient 创建MQTT客户端
func createMQTTClient(config EMQXConfig) mqtt.Client {
	opts := mqtt.NewClientOptions()
	opts.AddBroker(fmt.Sprintf("tcp://%s:%d", config.Broker, config.Port))
	opts.SetClientID(config.ClientID)
	opts.SetUsername(config.Username)
	opts.SetPassword(config.Password)
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(1 * time.Second)
	opts.SetConnectTimeout(5 * time.Second)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(10 * time.Second)

	// 连接丢失处理
	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		fmt.Printf("❌ 连接丢失: %v\n", err)
	})

	// 重连成功处理
	opts.SetOnConnectHandler(func(client mqtt.Client) {
		fmt.Println("🔄 重新连接成功")
	})

	return mqtt.NewClient(opts)
}

// startPublishing 开始发布测试数据
func startPublishing(client mqtt.Client, topic string, stopChan chan os.Signal) {
	ticker := time.NewTicker(50 * time.Millisecond) // 每2秒发送一条消息
	defer ticker.Stop()

	messageCount := 0
	startTime := time.Now()

	for {
		select {
		case <-ticker.C:
			// 生成测试消息
			message := generateTestMessage()
			messageCount++

			// 序列化为JSON
			jsonData, err := json.Marshal(message)
			if err != nil {
				fmt.Printf("❌ JSON序列化失败: %v\n", err)
				continue
			}

			// 发布消息
			token := client.Publish(topic, 0, false, jsonData)
			if token.Wait() && token.Error() != nil {
				fmt.Printf("❌ 消息发送失败: %v\n", token.Error())
				continue
			}

			// 显示发送状态
			elapsed := time.Since(startTime)
			rate := float64(messageCount) / elapsed.Seconds()
			fmt.Printf("📤 [%d] 消息已发送 | 速率: %.2f msg/s | 设备: %s | 时间: %s\n",
				messageCount,
				rate,
				message.DeviceID,
				message.Timestamp.Format("15:04:05"))

		case <-stopChan:
			// 发送最后的统计信息
			elapsed := time.Since(startTime)
			avgRate := float64(messageCount) / elapsed.Seconds()
			fmt.Printf("\n📊 发送统计:\n")
			fmt.Printf("   总消息数: %d\n", messageCount)
			fmt.Printf("   运行时间: %v\n", elapsed.Round(time.Second))
			fmt.Printf("   平均速率: %.2f msg/s\n", avgRate)
			return
		}
	}
}

// generateTestMessage 生成测试消息
func generateTestMessage() TestMessage {
	// 模拟设备在北京附近移动
	baseLatitude := 39.9042 + (rand.Float64()-0.5)*0.1   // 北京纬度 ± 0.05度
	baseLongitude := 116.4074 + (rand.Float64()-0.5)*0.1 // 北京经度 ± 0.05度

	statuses := []string{"online", "moving", "idle", "charging"}

	return TestMessage{
		DeviceID:  "RA0002",
		Timestamp: time.Now(),
		Location: Location{
			Latitude:  baseLatitude,
			Longitude: baseLongitude,
			Altitude:  50.0 + rand.Float64()*100, // 50-150米
		},
		Speed:   rand.Float64() * 80,                // 0-80 km/h
		Status:  statuses[rand.Intn(len(statuses))], // 随机状态
		Battery: 20 + rand.Intn(80),                 // 20-100%
	}
}
