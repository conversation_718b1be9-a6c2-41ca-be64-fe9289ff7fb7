package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"monitor/internal/agent/api"
	"monitor/internal/agent/config"
	"monitor/internal/shared/logger"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志系统
	err = logger.Init()
	if err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}
	logger.Info("启动监控代理", "version", "1.0.0")

	// 创建API处理器
	handler := api.NewHandler(cfg)

	// 设置路由
	router := handler.SetupRoutes()

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// 启动HTTP服务器
	go func() {
		logger.Info("启动Agent服务器",
			"host", cfg.Server.Host,
			"port", cfg.Server.Port,
			"token", maskToken(cfg.Security.Token))

		if cfg.Security.EnableTLS {
			if cfg.Security.CertFile == "" || cfg.Security.KeyFile == "" {
				logger.Fatal("TLS已启用但未指定cert_file或key_file")
			}
			logger.Info("启动HTTPS服务器")
			if err := server.ListenAndServeTLS(cfg.Security.CertFile, cfg.Security.KeyFile); err != nil && err != http.ErrServerClosed {
				logger.Fatal("启动HTTPS服务器失败", "error", err)
			}
		} else {
			logger.Info("启动HTTP服务器")
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				logger.Fatal("启动HTTP服务器失败", "error", err)
			}
		}
	}()

	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭Agent服务器...")

	// 创建一个5秒的超时上下文用于关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("强制关闭服务器", "error", err)
	}

	logger.Info("Agent服务器已停止")
}

// maskToken 遮蔽token用于日志记录
func maskToken(token string) string {
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}
