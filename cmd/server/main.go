package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/handler"
	"monitor/internal/server/service"
	"monitor/internal/shared/logger"
	"monitor/pkg/database"
	"monitor/pkg/nacos"
)

func main() {
	// 初始化日志系统（开发环境使用控制台格式）
	if err := logger.Init(); err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}

	// 第一步：加载本地Nacos连接配置
	localCfg, err := config.LoadLocal()
	if err != nil {
		logger.Fatal("加载本地Nacos配置失败", "error", err)
	}

	// 第二步：连接Nacos配置中心
	nacosClient, err := nacos.New(&localCfg.Nacos)
	if err != nil {
		logger.Fatal("连接Nacos失败", "error", err)
	}
	defer nacosClient.Close()

	// 第三步：从Nacos获取完整应用配置并解析服务
	cfg, err := config.LoadAndResolveFromNacos(nacosClient)
	if err != nil {
		logger.Fatal("从Nacos加载和解析配置失败", "error", err)
	}

	// 立即根据配置重新初始化日志系统
	if err := logger.InitWithFormat(cfg.Logging.Format); err != nil {
		logger.Fatal("使用Nacos配置重新初始化日志系统失败", "error", err)
	}

	// 第四步：启动配置监听（热更新）
	if err := config.StartConfigWatch(nacosClient, cfg); err != nil {
		logger.Warn("启动配置监听失败", "error", err)
		// 继续运行，但没有热更新功能
	}

	startServerWithConfig(cfg)
}

// startServerWithConfig 使用指定配置启动服务器
func startServerWithConfig(cfg *config.Config) {
	// 初始化数据库
	if err := database.Init(&cfg.Database); err != nil {
		logger.Fatal("初始化数据库失败", "error", err)
	}
	defer database.Close()

	// 创建监控服务
	monitorSvc := service.NewMonitorService(cfg)

	// 启动前预检查所有服务
	logger.Info("执行服务预检查...")
	if err := monitorSvc.PreCheck(); err != nil {
		logger.Fatal("服务预检查失败，退出程序", "error", err)
	}
	logger.Info("服务预检查成功完成")

	// 启动监控服务
	if err := monitorSvc.Start(); err != nil {
		logger.Fatal("启动监控服务失败", "error", err)
	}
	defer monitorSvc.Stop()

	// 创建HTTP处理器
	h := handler.New(cfg)

	// 设置Gin路由
	router := h.SetupRoutes()

	// 创建HTTP服务器（标准方式，支持优雅关闭和更多配置）
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// 启动服务器
	go func() {
		logger.Info("启动监控服务器", "port", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("启动服务器失败", "error", err)
		}
	}()

	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭服务器...")

	// 创建一个5秒的超时上下文用于关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("强制关闭服务器", "error", err)
	}

	logger.Info("服务器已退出")
}
