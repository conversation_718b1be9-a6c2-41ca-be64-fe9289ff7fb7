package repository

import (
	"time"

	"monitor/internal/server/model"
	"monitor/pkg/database"

	"gorm.io/gorm"
)

// AlertRepository 告警数据访问层
type AlertRepository struct {
	db *gorm.DB
}

// NewAlertRepository 创建告警仓库实例
func NewAlertRepository() *AlertRepository {
	return &AlertRepository{
		db: database.DB,
	}
}

// Create 创建告警记录
func (r *AlertRepository) Create(alert *model.Alert) error {
	return r.db.Create(alert).Error
}

// GetByID 根据ID获取告警记录
func (r *AlertRepository) GetByID(id uint) (*model.Alert, error) {
	var alert model.Alert
	err := r.db.Preload("Notifications").First(&alert, id).Error
	if err != nil {
		return nil, err
	}
	return &alert, nil
}

// Update 更新告警记录
func (r *AlertRepository) Update(alert *model.Alert) error {
	return r.db.Save(alert).Error
}

// Delete 删除告警记录（硬删除）
func (r *AlertRepository) Delete(id uint) error {
	return r.db.Delete(&model.Alert{}, id).Error
}

// GetActiveAlerts 获取活跃告警列表
func (r *AlertRepository) GetActiveAlerts() ([]model.Alert, error) {
	var alerts []model.Alert
	err := r.db.Where("status IN ?", []model.AlertStatus{
		model.AlertStatusPending,
		model.AlertStatusFiring,
	}).Order("severity DESC, started_at ASC").Find(&alerts).Error

	return alerts, err
}

// GetAlertsByService 根据服务名获取告警列表
func (r *AlertRepository) GetAlertsByService(serviceName string) ([]model.Alert, error) {
	var alerts []model.Alert
	err := r.db.Where("service_name = ?", serviceName).
		Order("started_at DESC").
		Find(&alerts).Error

	return alerts, err
}

// GetActiveAlertsByService 根据服务名获取活跃告警列表
func (r *AlertRepository) GetActiveAlertsByService(serviceName string) ([]model.Alert, error) {
	var alerts []model.Alert
	err := r.db.Where("service_name = ? AND status IN ?", serviceName, []model.AlertStatus{
		model.AlertStatusPending,
		model.AlertStatusFiring,
	}).Order("started_at DESC").Find(&alerts).Error

	return alerts, err
}

// GetAlertsByTimeRange 根据时间范围获取告警列表
func (r *AlertRepository) GetAlertsByTimeRange(startTime, endTime time.Time) ([]model.Alert, error) {
	var alerts []model.Alert
	err := r.db.Where("started_at BETWEEN ? AND ?", startTime, endTime).
		Order("started_at DESC").
		Find(&alerts).Error

	return alerts, err
}

// GetAlertsByStatus 根据状态获取告警列表
func (r *AlertRepository) GetAlertsByStatus(status model.AlertStatus) ([]model.Alert, error) {
	var alerts []model.Alert
	err := r.db.Where("status = ?", status).
		Order("started_at DESC").
		Find(&alerts).Error

	return alerts, err
}

// CountActiveAlerts 统计活跃告警数量
func (r *AlertRepository) CountActiveAlerts() (int64, error) {
	var count int64
	err := r.db.Model(&model.Alert{}).
		Where("status IN ?", []model.AlertStatus{
			model.AlertStatusPending,
			model.AlertStatusFiring,
		}).Count(&count).Error

	return count, err
}

// CountAlertsByService 统计指定服务的告警数量
func (r *AlertRepository) CountAlertsByService(serviceName string) (int64, error) {
	var count int64
	err := r.db.Model(&model.Alert{}).
		Where("service_name = ?", serviceName).
		Count(&count).Error

	return count, err
}

// ResolveAlert 解决告警
func (r *AlertRepository) ResolveAlert(id uint) error {
	now := time.Now()
	return r.db.Model(&model.Alert{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":      model.AlertStatusResolved,
			"resolved_at": now,
			"updated_at":  now,
		}).Error
}

// GetAlertStatistics 获取告警统计信息
func (r *AlertRepository) GetAlertStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总告警数
	var totalCount int64
	if err := r.db.Model(&model.Alert{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats["total"] = totalCount

	// 活跃告警数
	var activeCount int64
	if err := r.db.Model(&model.Alert{}).
		Where("status IN ?", []model.AlertStatus{
			model.AlertStatusPending,
			model.AlertStatusFiring,
		}).Count(&activeCount).Error; err != nil {
		return nil, err
	}
	stats["active"] = activeCount

	// 按严重级别统计
	var severityStats []struct {
		Severity model.AlertSeverity `json:"severity"`
		Count    int64               `json:"count"`
	}
	if err := r.db.Model(&model.Alert{}).
		Select("severity, count(*) as count").
		Where("status IN ?", []model.AlertStatus{
			model.AlertStatusPending,
			model.AlertStatusFiring,
		}).
		Group("severity").
		Find(&severityStats).Error; err != nil {
		return nil, err
	}
	stats["by_severity"] = severityStats

	// 按服务统计
	var serviceStats []struct {
		ServiceName string `json:"service_name"`
		Count       int64  `json:"count"`
	}
	if err := r.db.Model(&model.Alert{}).
		Select("service_name, count(*) as count").
		Where("status IN ?", []model.AlertStatus{
			model.AlertStatusPending,
			model.AlertStatusFiring,
		}).
		Group("service_name").
		Find(&serviceStats).Error; err != nil {
		return nil, err
	}
	stats["by_service"] = serviceStats

	return stats, nil
}

// FindExistingAlert 查找已存在的相同告警
func (r *AlertRepository) FindExistingAlert(ruleName, serviceName string) (*model.Alert, error) {
	var alert model.Alert
	err := r.db.Where("rule_name = ? AND service_name = ? AND status IN ?",
		ruleName, serviceName, []model.AlertStatus{
			model.AlertStatusPending,
			model.AlertStatusFiring,
		}).First(&alert).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return &alert, err
}
