package repository

import (
	"time"

	"monitor/internal/server/model"
	"monitor/pkg/database"

	"gorm.io/gorm"
)

// NotificationRepository 通知数据访问层
type NotificationRepository struct {
	db *gorm.DB
}

// NewNotificationRepository 创建通知仓库实例
func NewNotificationRepository() *NotificationRepository {
	return &NotificationRepository{
		db: database.DB,
	}
}

// Create 创建通知记录
func (r *NotificationRepository) Create(notification *model.Notification) error {
	return r.db.Create(notification).Error
}

// Update 更新通知记录
func (r *NotificationRepository) Update(notification *model.Notification) error {
	return r.db.Save(notification).Error
}

// GetByID 根据ID获取通知记录
func (r *NotificationRepository) GetByID(id uint) (*model.Notification, error) {
	var notification model.Notification
	err := r.db.Preload("Alert").First(&notification, id).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

// GetByAlertID 获取告警相关的所有通知
func (r *NotificationRepository) GetByAlertID(alertID uint) ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("alert_id = ?", alertID).
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// GetPendingNotifications 获取待发送通知
func (r *NotificationRepository) GetPendingNotifications() ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("status = ?", model.NotificationStatusPending).
		Preload("Alert").
		Order("created_at ASC").
		Find(&notifications).Error
	return notifications, err
}

// GetFailedNotifications 获取发送失败的通知
func (r *NotificationRepository) GetFailedNotifications(maxRetries int) ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("status = ? AND retry_count < ?",
		model.NotificationStatusFailed, maxRetries).
		Preload("Alert").
		Order("created_at ASC").
		Find(&notifications).Error
	return notifications, err
}

// GetNotificationsByChannel 根据通知渠道获取通知记录
func (r *NotificationRepository) GetNotificationsByChannel(channel model.NotificationChannel) ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("channel = ?", channel).
		Preload("Alert").
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// GetNotificationsByStatus 根据状态获取通知记录
func (r *NotificationRepository) GetNotificationsByStatus(status model.NotificationStatus) ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("status = ?", status).
		Preload("Alert").
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// GetNotificationsByRecipient 根据接收者获取通知记录
func (r *NotificationRepository) GetNotificationsByRecipient(recipient string) ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("recipient = ?", recipient).
		Preload("Alert").
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// GetNotificationsInTimeRange 获取指定时间范围内的通知记录
func (r *NotificationRepository) GetNotificationsInTimeRange(startTime, endTime time.Time) ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("created_at BETWEEN ? AND ?", startTime, endTime).
		Preload("Alert").
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// UpdateStatus 更新通知状态
func (r *NotificationRepository) UpdateStatus(id uint, status model.NotificationStatus, errorMessage string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if status == model.NotificationStatusSent {
		now := time.Now()
		updates["sent_at"] = &now
		updates["error_message"] = ""
	} else if status == model.NotificationStatusFailed {
		updates["error_message"] = errorMessage
		// 增加重试次数
		return r.db.Model(&model.Notification{}).
			Where("id = ?", id).
			Updates(updates).
			Update("retry_count", gorm.Expr("retry_count + 1")).Error
	}

	return r.db.Model(&model.Notification{}).Where("id = ?", id).Updates(updates).Error
}

// MarkAsSent 标记通知为已发送
func (r *NotificationRepository) MarkAsSent(id uint) error {
	return r.UpdateStatus(id, model.NotificationStatusSent, "")
}

// MarkAsFailed 标记通知为发送失败
func (r *NotificationRepository) MarkAsFailed(id uint, errorMessage string) error {
	return r.UpdateStatus(id, model.NotificationStatusFailed, errorMessage)
}

// GetNotificationStats 获取通知统计信息
func (r *NotificationRepository) GetNotificationStats() (map[string]int64, error) {
	stats := make(map[string]int64)

	// 统计各状态的通知数量
	var pendingCount, sentCount, failedCount int64

	if err := r.db.Model(&model.Notification{}).
		Where("status = ?", model.NotificationStatusPending).
		Count(&pendingCount).Error; err != nil {
		return nil, err
	}

	if err := r.db.Model(&model.Notification{}).
		Where("status = ?", model.NotificationStatusSent).
		Count(&sentCount).Error; err != nil {
		return nil, err
	}

	if err := r.db.Model(&model.Notification{}).
		Where("status = ?", model.NotificationStatusFailed).
		Count(&failedCount).Error; err != nil {
		return nil, err
	}

	stats["pending"] = pendingCount
	stats["sent"] = sentCount
	stats["failed"] = failedCount
	stats["total"] = pendingCount + sentCount + failedCount

	return stats, nil
}

// GetNotificationStatsByChannel 获取按渠道分组的通知统计
func (r *NotificationRepository) GetNotificationStatsByChannel() (map[model.NotificationChannel]int64, error) {
	stats := make(map[model.NotificationChannel]int64)

	type ChannelStat struct {
		Channel model.NotificationChannel
		Count   int64
	}

	var channelStats []ChannelStat
	err := r.db.Model(&model.Notification{}).
		Select("channel, COUNT(*) as count").
		Group("channel").
		Scan(&channelStats).Error

	if err != nil {
		return nil, err
	}

	for _, stat := range channelStats {
		stats[stat.Channel] = stat.Count
	}

	return stats, nil
}

// DeleteOldNotifications 删除指定时间之前的通知记录
func (r *NotificationRepository) DeleteOldNotifications(beforeTime time.Time) (int64, error) {
	result := r.db.Where("created_at < ?", beforeTime).Delete(&model.Notification{})
	return result.RowsAffected, result.Error
}

// BatchCreate 批量创建通知记录
func (r *NotificationRepository) BatchCreate(notifications []model.Notification) error {
	if len(notifications) == 0 {
		return nil
	}
	return r.db.CreateInBatches(notifications, 100).Error
}

// GetRecentFailedNotifications 获取最近失败的通知（用于监控）
func (r *NotificationRepository) GetRecentFailedNotifications(since time.Time) ([]model.Notification, error) {
	var notifications []model.Notification
	err := r.db.Where("status = ? AND updated_at >= ?",
		model.NotificationStatusFailed, since).
		Preload("Alert").
		Order("updated_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// CountNotificationsByAlertID 统计指定告警的通知数量
func (r *NotificationRepository) CountNotificationsByAlertID(alertID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.Notification{}).
		Where("alert_id = ?", alertID).
		Count(&count).Error
	return count, err
}
