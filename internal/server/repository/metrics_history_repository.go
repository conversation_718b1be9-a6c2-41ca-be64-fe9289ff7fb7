package repository

import (
	"time"

	"monitor/internal/server/model"
	"monitor/internal/shared/logger"
	"monitor/pkg/database"

	"gorm.io/gorm"
)

// MetricsHistoryRepository 指标历史数据仓库
type MetricsHistoryRepository struct {
	db *gorm.DB
}

// NewMetricsHistoryRepository 创建指标历史数据仓库实例
func NewMetricsHistoryRepository() *MetricsHistoryRepository {
	return &MetricsHistoryRepository{
		db: database.DB,
	}
}

// Create 创建指标历史记录
func (r *MetricsHistoryRepository) Create(metrics *model.MetricsHistory) error {
	if err := r.db.Create(metrics).Error; err != nil {
		logger.Error("创建指标历史记录失败", "error", err)
		return err
	}
	return nil
}

// GetByServiceName 根据服务名称获取指标历史
func (r *MetricsHistoryRepository) GetByServiceName(serviceName string, limit int) ([]*model.MetricsHistory, error) {
	var metrics []*model.MetricsHistory

	query := r.db.Where("service_name = ?", serviceName).
		Order("collected_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&metrics).Error; err != nil {
		logger.Error("根据服务名称获取指标历史失败", "service", serviceName, "error", err)
		return nil, err
	}

	return metrics, nil
}

// GetByServiceAndMetric 根据服务名称和指标名称获取历史数据
func (r *MetricsHistoryRepository) GetByServiceAndMetric(serviceName, metricName string, limit int) ([]*model.MetricsHistory, error) {
	var metrics []*model.MetricsHistory

	query := r.db.Where("service_name = ? AND metric_name = ?", serviceName, metricName).
		Order("collected_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&metrics).Error; err != nil {
		logger.Error("根据服务和指标获取指标历史失败",
			"service", serviceName,
			"metric", metricName,
			"error", err)
		return nil, err
	}

	return metrics, nil
}

// GetByTimeRange 根据时间范围获取指标数据
func (r *MetricsHistoryRepository) GetByTimeRange(serviceName, metricName string, startTime, endTime time.Time) ([]*model.MetricsHistory, error) {
	var metrics []*model.MetricsHistory

	query := r.db.Where("service_name = ? AND metric_name = ? AND collected_at BETWEEN ? AND ?",
		serviceName, metricName, startTime, endTime).
		Order("collected_at ASC")

	if err := query.Find(&metrics).Error; err != nil {
		logger.Error("根据时间范围获取指标历史失败",
			"service", serviceName,
			"metric", metricName,
			"start_time", startTime,
			"end_time", endTime,
			"error", err)
		return nil, err
	}

	return metrics, nil
}

// GetLatestByService 获取服务的最新指标数据
func (r *MetricsHistoryRepository) GetLatestByService(serviceName string) ([]*model.MetricsHistory, error) {
	var metrics []*model.MetricsHistory

	// 获取每个指标类型的最新记录
	subQuery := r.db.Model(&model.MetricsHistory{}).
		Select("metric_name, MAX(collected_at) as max_collected_at").
		Where("service_name = ?", serviceName).
		Group("metric_name")

	if err := r.db.Where("service_name = ? AND (metric_name, collected_at) IN (?)", serviceName, subQuery).
		Find(&metrics).Error; err != nil {
		logger.Error("Failed to get latest metrics by service", "service", serviceName, "error", err)
		return nil, err
	}

	return metrics, nil
}

// DeleteOldRecords 删除旧的指标记录
func (r *MetricsHistoryRepository) DeleteOldRecords(beforeTime time.Time) error {
	result := r.db.Where("collected_at < ?", beforeTime).Delete(&model.MetricsHistory{})
	if result.Error != nil {
		logger.Error("删除旧指标记录失败", "before_time", beforeTime, "error", result.Error)
		return result.Error
	}

	logger.Info("已删除旧指标记录", "count", result.RowsAffected, "before_time", beforeTime)
	return nil
}

// GetMetricStats 获取指标统计信息
func (r *MetricsHistoryRepository) GetMetricStats(serviceName, metricName string, startTime, endTime time.Time) (map[string]interface{}, error) {
	var result struct {
		Count   int64   `json:"count"`
		Average float64 `json:"average"`
		Min     float64 `json:"min"`
		Max     float64 `json:"max"`
	}

	if err := r.db.Model(&model.MetricsHistory{}).
		Select("COUNT(*) as count, AVG(metric_value) as average, MIN(metric_value) as min, MAX(metric_value) as max").
		Where("service_name = ? AND metric_name = ? AND collected_at BETWEEN ? AND ?",
			serviceName, metricName, startTime, endTime).
		Scan(&result).Error; err != nil {
		logger.Error("获取指标统计信息失败",
			"service", serviceName,
			"metric", metricName,
			"error", err)
		return nil, err
	}

	stats := map[string]interface{}{
		"count":   result.Count,
		"average": result.Average,
		"min":     result.Min,
		"max":     result.Max,
	}

	return stats, nil
}

// BatchCreate 批量创建指标记录
func (r *MetricsHistoryRepository) BatchCreate(metrics []*model.MetricsHistory) error {
	if len(metrics) == 0 {
		return nil
	}

	if err := r.db.CreateInBatches(metrics, 100).Error; err != nil {
		logger.Error("Failed to batch create metrics history", "count", len(metrics), "error", err)
		return err
	}

	logger.Debug("Batch created metrics history", "count", len(metrics))
	return nil
}
