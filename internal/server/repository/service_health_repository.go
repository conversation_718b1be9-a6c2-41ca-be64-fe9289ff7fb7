package repository

import (
	"time"

	"monitor/internal/server/model"
	"monitor/pkg/database"

	"gorm.io/gorm"
)

// ServiceHealthRepository 服务健康状态数据访问层
type ServiceHealthRepository struct {
	db *gorm.DB
}

// NewServiceHealthRepository 创建服务健康状态仓库实例
func NewServiceHealthRepository() *ServiceHealthRepository {
	return &ServiceHealthRepository{
		db: database.DB,
	}
}

// CreateOrUpdate 创建或更新服务健康状态
func (r *ServiceHealthRepository) CreateOrUpdate(serviceHealth *model.ServiceHealth) error {
	// 确保实例ID不为空，使用默认值
	if serviceHealth.InstanceID == "" {
		serviceHealth.InstanceID = "default"
	}
	// 使用GORM的Upsert功能，基于复合主键(service_name, instance_id)
	return r.db.Save(serviceHealth).Error
}

// GetByServiceName 根据服务名获取健康状态（向后兼容方法）
// 优先返回默认实例，如果不存在则返回第一个实例
func (r *ServiceHealthRepository) GetByServiceName(serviceName string) (*model.ServiceHealth, error) {
	var serviceHealth model.ServiceHealth

	// 首先尝试获取默认实例
	err := r.db.Where("service_name = ? AND instance_id = ?", serviceName, "default").First(&serviceHealth).Error
	if err == nil {
		return &serviceHealth, nil
	}

	// 如果默认实例不存在，获取第一个实例
	if err == gorm.ErrRecordNotFound {
		err = r.db.Where("service_name = ?", serviceName).
			Order("instance_id ASC").
			First(&serviceHealth).Error
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
	}

	return &serviceHealth, err
}

// GetByServiceNameAndInstance 根据服务名和实例ID获取健康状态
func (r *ServiceHealthRepository) GetByServiceNameAndInstance(serviceName, instanceID string) (*model.ServiceHealth, error) {
	var serviceHealth model.ServiceHealth
	err := r.db.Where("service_name = ? AND instance_id = ?", serviceName, instanceID).First(&serviceHealth).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &serviceHealth, err
}

// GetInstancesByServiceName 获取服务的所有实例
func (r *ServiceHealthRepository) GetInstancesByServiceName(serviceName string) ([]model.ServiceHealth, error) {
	var instances []model.ServiceHealth
	err := r.db.Where("service_name = ?", serviceName).
		Order("instance_id ASC").
		Find(&instances).Error
	return instances, err
}

// GetAllInstances 获取所有服务实例
func (r *ServiceHealthRepository) GetAllInstances() ([]model.ServiceHealth, error) {
	var instances []model.ServiceHealth
	err := r.db.Order("service_name ASC, instance_id ASC").Find(&instances).Error
	return instances, err
}

// DeleteInstance 删除特定服务实例
func (r *ServiceHealthRepository) DeleteInstance(serviceName, instanceID string) error {
	return r.db.Where("service_name = ? AND instance_id = ?", serviceName, instanceID).
		Delete(&model.ServiceHealth{}).Error
}

// GetAll 获取所有服务健康状态
func (r *ServiceHealthRepository) GetAll() ([]model.ServiceHealth, error) {
	var services []model.ServiceHealth
	err := r.db.Order("service_name ASC").Find(&services).Error
	return services, err
}

// GetByStatus 根据状态获取服务列表
func (r *ServiceHealthRepository) GetByStatus(status model.HealthStatus) ([]model.ServiceHealth, error) {
	var services []model.ServiceHealth
	err := r.db.Where("status = ?", status).
		Order("service_name ASC").
		Find(&services).Error
	return services, err
}

// GetUnhealthyServices 获取不健康的服务列表
func (r *ServiceHealthRepository) GetUnhealthyServices() ([]model.ServiceHealth, error) {
	return r.GetByStatus(model.HealthStatusUnhealthy)
}

// GetHealthyServices 获取健康的服务列表
func (r *ServiceHealthRepository) GetHealthyServices() ([]model.ServiceHealth, error) {
	return r.GetByStatus(model.HealthStatusHealthy)
}

// UpdateHealthStatus 更新服务健康状态（向后兼容方法，更新默认实例）
func (r *ServiceHealthRepository) UpdateHealthStatus(serviceName string, status model.HealthStatus, errorMsg string) error {
	return r.UpdateInstanceHealthStatus(serviceName, "default", status, errorMsg)
}

// UpdateInstanceHealthStatus 更新特定实例的健康状态
func (r *ServiceHealthRepository) UpdateInstanceHealthStatus(serviceName, instanceID string, status model.HealthStatus, errorMsg string) error {
	updates := map[string]interface{}{
		"status":     status,
		"last_check": time.Now(),
		"updated_at": time.Now(),
	}

	if status == model.HealthStatusHealthy {
		updates["error_message"] = ""
		updates["consecutive_failures"] = 0
	} else {
		updates["error_message"] = errorMsg
		// 增加连续失败次数
		r.db.Model(&model.ServiceHealth{}).
			Where("service_name = ? AND instance_id = ?", serviceName, instanceID).
			Update("consecutive_failures", gorm.Expr("consecutive_failures + 1"))
	}

	return r.db.Model(&model.ServiceHealth{}).
		Where("service_name = ? AND instance_id = ?", serviceName, instanceID).
		Updates(updates).Error
}

// GetServicesNeedingCheck 获取需要检查的服务列表
func (r *ServiceHealthRepository) GetServicesNeedingCheck(checkInterval time.Duration) ([]model.ServiceHealth, error) {
	var services []model.ServiceHealth
	cutoffTime := time.Now().Add(-checkInterval)

	err := r.db.Where("last_check < ?", cutoffTime).
		Order("last_check ASC").
		Find(&services).Error

	return services, err
}

// GetServiceStatistics 获取服务健康统计信息
func (r *ServiceHealthRepository) GetServiceStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总服务数
	var totalCount int64
	if err := r.db.Model(&model.ServiceHealth{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats["total"] = totalCount

	// 按状态统计
	var statusStats []struct {
		Status model.HealthStatus `json:"status"`
		Count  int64              `json:"count"`
	}
	if err := r.db.Model(&model.ServiceHealth{}).
		Select("status, count(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, err
	}
	stats["by_status"] = statusStats

	// 连续失败次数统计
	var failureStats []struct {
		ConsecutiveFailures int   `json:"consecutive_failures"`
		Count               int64 `json:"count"`
	}
	if err := r.db.Model(&model.ServiceHealth{}).
		Select("consecutive_failures, count(*) as count").
		Where("consecutive_failures > 0").
		Group("consecutive_failures").
		Order("consecutive_failures ASC").
		Find(&failureStats).Error; err != nil {
		return nil, err
	}
	stats["failure_distribution"] = failureStats

	return stats, nil
}

// Delete 删除服务的所有实例健康记录
func (r *ServiceHealthRepository) Delete(serviceName string) error {
	return r.db.Where("service_name = ?", serviceName).Delete(&model.ServiceHealth{}).Error
}

// DeleteAllInstancesOfService 删除服务的所有实例（别名方法，语义更清晰）
func (r *ServiceHealthRepository) DeleteAllInstancesOfService(serviceName string) error {
	return r.Delete(serviceName)
}

// GetServicesWithHighFailures 获取连续失败次数超过阈值的服务
func (r *ServiceHealthRepository) GetServicesWithHighFailures(threshold int) ([]model.ServiceHealth, error) {
	var services []model.ServiceHealth
	err := r.db.Where("consecutive_failures >= ?", threshold).
		Order("consecutive_failures DESC").
		Find(&services).Error

	return services, err
}

// ResetConsecutiveFailures 重置服务所有实例的连续失败次数
func (r *ServiceHealthRepository) ResetConsecutiveFailures(serviceName string) error {
	return r.db.Model(&model.ServiceHealth{}).
		Where("service_name = ?", serviceName).
		Update("consecutive_failures", 0).Error
}

// ResetInstanceConsecutiveFailures 重置特定实例的连续失败次数
func (r *ServiceHealthRepository) ResetInstanceConsecutiveFailures(serviceName, instanceID string) error {
	return r.db.Model(&model.ServiceHealth{}).
		Where("service_name = ? AND instance_id = ?", serviceName, instanceID).
		Update("consecutive_failures", 0).Error
}

// BulkUpdateLastCheck 批量更新服务的最后检查时间
func (r *ServiceHealthRepository) BulkUpdateLastCheck(serviceNames []string) error {
	return r.db.Model(&model.ServiceHealth{}).
		Where("service_name IN ?", serviceNames).
		Update("last_check", time.Now()).Error
}

// BulkUpdateInstancesLastCheck 批量更新特定实例的最后检查时间
func (r *ServiceHealthRepository) BulkUpdateInstancesLastCheck(instances []struct {
	ServiceName string
	InstanceID  string
}) error {
	if len(instances) == 0 {
		return nil
	}

	// 构建批量更新条件
	tx := r.db.Model(&model.ServiceHealth{})
	for i, instance := range instances {
		if i == 0 {
			tx = tx.Where("(service_name = ? AND instance_id = ?)", instance.ServiceName, instance.InstanceID)
		} else {
			tx = tx.Or("(service_name = ? AND instance_id = ?)", instance.ServiceName, instance.InstanceID)
		}
	}

	return tx.Update("last_check", time.Now()).Error
}

// GetInstancesByStatus 根据状态获取所有实例
func (r *ServiceHealthRepository) GetInstancesByStatus(status model.HealthStatus) ([]model.ServiceHealth, error) {
	var instances []model.ServiceHealth
	err := r.db.Where("status = ?", status).
		Order("service_name ASC, instance_id ASC").
		Find(&instances).Error
	return instances, err
}

// GetServiceInstanceStatistics 获取服务实例统计信息
func (r *ServiceHealthRepository) GetServiceInstanceStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总实例数
	var totalInstances int64
	if err := r.db.Model(&model.ServiceHealth{}).Count(&totalInstances).Error; err != nil {
		return nil, err
	}
	stats["total_instances"] = totalInstances

	// 总服务数（去重）
	var totalServices int64
	if err := r.db.Model(&model.ServiceHealth{}).
		Distinct("service_name").
		Count(&totalServices).Error; err != nil {
		return nil, err
	}
	stats["total_services"] = totalServices

	// 按服务统计实例数
	var serviceInstanceStats []struct {
		ServiceName   string `json:"service_name"`
		InstanceCount int64  `json:"instance_count"`
	}
	if err := r.db.Model(&model.ServiceHealth{}).
		Select("service_name, count(*) as instance_count").
		Group("service_name").
		Order("service_name ASC").
		Find(&serviceInstanceStats).Error; err != nil {
		return nil, err
	}
	stats["instances_per_service"] = serviceInstanceStats

	// 按状态统计实例数
	var statusStats []struct {
		Status model.HealthStatus `json:"status"`
		Count  int64              `json:"count"`
	}
	if err := r.db.Model(&model.ServiceHealth{}).
		Select("status, count(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, err
	}
	stats["instances_by_status"] = statusStats

	return stats, nil
}

// GetInstancesNeedingCheck 获取需要检查的实例列表
func (r *ServiceHealthRepository) GetInstancesNeedingCheck(checkInterval time.Duration) ([]model.ServiceHealth, error) {
	var instances []model.ServiceHealth
	cutoffTime := time.Now().Add(-checkInterval)

	err := r.db.Where("last_check < ?", cutoffTime).
		Order("last_check ASC").
		Find(&instances).Error

	return instances, err
}

// GetInstancesWithHighFailures 获取连续失败次数超过阈值的实例
func (r *ServiceHealthRepository) GetInstancesWithHighFailures(threshold int) ([]model.ServiceHealth, error) {
	var instances []model.ServiceHealth
	err := r.db.Where("consecutive_failures >= ?", threshold).
		Order("consecutive_failures DESC, service_name ASC, instance_id ASC").
		Find(&instances).Error

	return instances, err
}

// CountInstancesByService 统计每个服务的实例数量
func (r *ServiceHealthRepository) CountInstancesByService() (map[string]int64, error) {
	var results []struct {
		ServiceName   string `json:"service_name"`
		InstanceCount int64  `json:"instance_count"`
	}

	err := r.db.Model(&model.ServiceHealth{}).
		Select("service_name, count(*) as instance_count").
		Group("service_name").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	counts := make(map[string]int64)
	for _, result := range results {
		counts[result.ServiceName] = result.InstanceCount
	}

	return counts, nil
}
