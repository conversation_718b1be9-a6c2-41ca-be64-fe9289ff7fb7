package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/model"
	"monitor/internal/server/repository"
	"monitor/internal/shared/logger"
)

// MonitorService 监控服务
type MonitorService struct {
	config              *config.Config
	serviceHealthRepo   *repository.ServiceHealthRepository
	alertRepo           *repository.AlertRepository
	metricsRepo         *repository.MetricsHistoryRepository
	containerManager    *ContainerManager
	httpClient          *http.Client
	kafkaMonitors       map[string]*KafkaMonitor
	emqxMonitors        map[string]*EMQXMonitor
	notificationService *NotificationService
	stopChan            chan struct{}
	wg                  sync.WaitGroup
	mu                  sync.RWMutex
	startTime           time.Time // 服务启动时间
}

// NewMonitorService 创建监控服务实例
func NewMonitorService(cfg *config.Config) *MonitorService {
	// 初始化容器管理器
	containerManager, err := NewContainerManager()
	if err != nil {
		logger.Warn("初始化容器管理器失败，Docker功能将被禁用", "error", err)
		containerManager = nil
	} else {
		logger.Info("Docker容器管理器初始化成功")
	}

	// 初始化通知服务
	var notificationService *NotificationService
	if cfg.Notifications.Channels.SMS != nil || cfg.Notifications.Channels.Email != nil || cfg.Notifications.Channels.Webhook != nil {
		notificationService = NewNotificationService(cfg)
		logger.Info("通知服务已初始化")
	} else {
		logger.Info("通知服务已禁用 - 未配置通知渠道")
	}

	return &MonitorService{
		config:              cfg,
		serviceHealthRepo:   repository.NewServiceHealthRepository(),
		alertRepo:           repository.NewAlertRepository(),
		metricsRepo:         repository.NewMetricsHistoryRepository(),
		containerManager:    containerManager,
		notificationService: notificationService,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		kafkaMonitors: make(map[string]*KafkaMonitor),
		emqxMonitors:  make(map[string]*EMQXMonitor),
		stopChan:      make(chan struct{}),
	}
}

// PreCheck 启动前预检查服务（并发执行，只检查手动配置的服务）
func (ms *MonitorService) PreCheck() error {
	// 过滤需要预检查的服务（默认只检查手动配置的服务）
	servicesToCheck := ms.filterServicesForPreCheck()

	if len(servicesToCheck) == 0 {
		logger.Info("没有服务需要预检查")
		return nil
	}

	logger.Info("开始并发服务预检查",
		"services_to_check", len(servicesToCheck),
		"total_services", len(ms.config.Services))

	// 使用channel收集结果
	type checkResult struct {
		service config.ServiceConfig
		err     error
	}

	resultChan := make(chan checkResult, len(servicesToCheck))

	// 并发执行预检查
	for _, serviceConfig := range servicesToCheck {
		go func(svc config.ServiceConfig) {
			logger.Info("预检查服务", "service", svc.Name, "endpoint", svc.Endpoint)
			err := ms.preCheckService(svc)
			resultChan <- checkResult{service: svc, err: err}
		}(serviceConfig)
	}

	// 收集结果
	var failedServices []string
	for i := 0; i < len(servicesToCheck); i++ {
		result := <-resultChan
		if result.err != nil {
			logger.Error("服务预检查失败",
				"service", result.service.Name,
				"endpoint", result.service.Endpoint,
				"error", result.err)
			failedServices = append(failedServices, fmt.Sprintf("%s (%s): %v",
				result.service.Name, result.service.Endpoint, result.err))
		} else {
			logger.Info("服务预检查通过", "service", result.service.Name)
		}
	}

	if len(failedServices) > 0 {
		return fmt.Errorf("预检查失败，%d个服务:\n  - %s",
			len(failedServices),
			strings.Join(failedServices, "\n  - "))
	}

	logger.Info("所有服务预检查通过")
	return nil
}

// filterServicesForPreCheck 过滤需要预检查的服务
func (ms *MonitorService) filterServicesForPreCheck() []config.ServiceConfig {
	var servicesToCheck []config.ServiceConfig

	// 默认对所有服务进行预检查，包括自动发现的服务
	for _, service := range ms.config.Services {
		servicesToCheck = append(servicesToCheck, service)
		logger.Debug("添加服务到预检查列表",
			"service", service.Name,
			"is_discovered", service.IsDiscovered,
			"type", service.Type)
	}

	return servicesToCheck
}

// Start 启动监控服务
func (ms *MonitorService) Start() error {
	// 记录服务启动时间
	ms.startTime = time.Now()
	logger.Info("启动监控服务", "services_count", len(ms.config.Services), "start_time", ms.startTime.Format("2006-01-02 15:04:05"))

	// 为每个服务启动监控goroutine
	for _, serviceConfig := range ms.config.Services {
		if serviceConfig.InstanceID != "" {
			// 实例级别监控
			ms.wg.Add(1)
			go ms.monitorServiceInstance(serviceConfig)
			logger.Debug("启动实例级别监控",
				"service", serviceConfig.Name,
				"instance_id", serviceConfig.InstanceID,
				"endpoint", serviceConfig.Endpoint)
		} else {
			// 传统服务级别监控
			ms.wg.Add(1)
			go ms.monitorService(serviceConfig)
			logger.Debug("启动服务级别监控",
				"service", serviceConfig.Name,
				"endpoint", serviceConfig.Endpoint)
		}
	}

	logger.Info("监控服务启动成功")
	return nil
}

// Stop 停止监控服务
func (ms *MonitorService) Stop() error {
	logger.Info("正在停止监控服务...")

	close(ms.stopChan)
	ms.wg.Wait()

	// 关闭Kafka监控器
	ms.mu.Lock()
	for serviceName, kafkaMonitor := range ms.kafkaMonitors {
		if kafkaMonitor != nil {
			if err := kafkaMonitor.Close(); err != nil {
				logger.Error("关闭Kafka监控器失败", "service", serviceName, "error", err)
			}
		}
	}
	ms.kafkaMonitors = make(map[string]*KafkaMonitor)

	// 关闭EMQX监控器
	for serviceName, emqxMonitor := range ms.emqxMonitors {
		if emqxMonitor != nil {
			if err := emqxMonitor.Close(); err != nil {
				logger.Error("关闭EMQX监控器失败", "service", serviceName, "error", err)
			}
		}
	}
	ms.emqxMonitors = make(map[string]*EMQXMonitor)
	ms.mu.Unlock()

	// 关闭容器管理器
	if ms.containerManager != nil {
		if err := ms.containerManager.Close(); err != nil {
			logger.Error("关闭容器管理器失败", "error", err)
		}
	}

	// 停止通知服务
	if ms.notificationService != nil {
		ms.notificationService.Stop()
		logger.Info("通知服务已停止")
	}

	logger.Info("监控服务已停止")
	return nil
}

// monitorService 监控单个服务（传统模式）
func (ms *MonitorService) monitorService(serviceConfig config.ServiceConfig) {
	defer ms.wg.Done()

	logger.Info("启动服务监控", "服务", serviceConfig.Name, "端点", serviceConfig.Endpoint)

	ticker := time.NewTicker(time.Duration(serviceConfig.CheckInterval) * time.Second)
	defer ticker.Stop()

	// 立即执行一次检查
	ms.checkService(serviceConfig)

	for {
		select {
		case <-ms.stopChan:
			logger.Info("停止服务监控", "服务", serviceConfig.Name)
			return
		case <-ticker.C:
			ms.checkService(serviceConfig)
		}
	}
}

// monitorServiceInstance 监控单个服务实例（多实例模式）
func (ms *MonitorService) monitorServiceInstance(serviceConfig config.ServiceConfig) {
	defer ms.wg.Done()

	logger.Info("启动实例监控",
		"服务", serviceConfig.Name,
		"实例ID", serviceConfig.InstanceID,
		"端点", serviceConfig.Endpoint)

	ticker := time.NewTicker(time.Duration(serviceConfig.CheckInterval) * time.Second)
	defer ticker.Stop()

	// 立即执行一次检查
	ms.checkServiceInstance(serviceConfig)

	for {
		select {
		case <-ms.stopChan:
			logger.Info("停止实例监控",
				"服务", serviceConfig.Name,
				"实例ID", serviceConfig.InstanceID)
			return
		case <-ticker.C:
			ms.checkServiceInstance(serviceConfig)
		}
	}
}

// preCheckService 预检查单个服务（返回错误而不是处理）
func (ms *MonitorService) preCheckService(serviceConfig config.ServiceConfig) error {
	switch serviceConfig.Type {
	case "http":
		return ms.preCheckHTTPService(serviceConfig)
	case "emqx":
		return ms.preCheckEMQXService(serviceConfig)
	case "business_api":
		return ms.preCheckHTTPService(serviceConfig) // 业务API也使用HTTP检查
	case "kafka":
		return ms.preCheckKafkaService(serviceConfig)
	default:
		return fmt.Errorf("未知服务类型: %s", serviceConfig.Type)
	}
}

// checkService 检查单个服务健康状态（传统模式）
func (ms *MonitorService) checkService(serviceConfig config.ServiceConfig) {
	switch serviceConfig.Type {
	case "http":
		ms.checkHTTPService(serviceConfig)
	case "emqx":
		ms.checkEMQXService(serviceConfig)
	case "business_api":
		ms.checkBusinessAPIService(serviceConfig)
	case "kafka":
		ms.checkKafkaService(serviceConfig)
	default:
		logger.Warn("未知服务类型", "服务", serviceConfig.Name, "类型", serviceConfig.Type)
	}
}

// checkServiceInstance 检查单个服务实例健康状态（多实例模式）
func (ms *MonitorService) checkServiceInstance(serviceConfig config.ServiceConfig) {
	switch serviceConfig.Type {
	case "http":
		ms.checkHTTPServiceInstance(serviceConfig)
	case "emqx":
		ms.checkEMQXServiceInstance(serviceConfig)
	case "business_api":
		ms.checkBusinessAPIServiceInstance(serviceConfig)
	case "kafka":
		ms.checkKafkaServiceInstance(serviceConfig)
	default:
		logger.Warn("未知服务实例类型",
			"服务", serviceConfig.Name,
			"实例ID", serviceConfig.InstanceID,
			"类型", serviceConfig.Type)
	}
}

// preCheckHTTPService 预检查HTTP服务（返回错误而不是处理）
func (ms *MonitorService) preCheckHTTPService(serviceConfig config.ServiceConfig) error {
	// 创建请求上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(serviceConfig.Timeout)*time.Second)
	defer cancel()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, serviceConfig.Method, serviceConfig.Endpoint, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 添加认证信息（如果有）
	if serviceConfig.Auth != nil {
		req.SetBasicAuth(serviceConfig.Auth.Username, serviceConfig.Auth.Password)
	}

	// 执行请求（带重试）
	var resp *http.Response
	var requestErr error

	for attempt := 0; attempt <= serviceConfig.RetryCount; attempt++ {
		resp, requestErr = ms.httpClient.Do(req)
		if requestErr == nil {
			break
		}

		if attempt < serviceConfig.RetryCount {
			time.Sleep(time.Second * time.Duration(attempt+1)) // 递增延迟
		}
	}

	if requestErr != nil {
		return fmt.Errorf("HTTP请求重试%d次后失败: %w", serviceConfig.RetryCount+1, requestErr)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	statusOK := false
	for _, expectedStatus := range serviceConfig.ExpectedStatus {
		if resp.StatusCode == expectedStatus {
			statusOK = true
			break
		}
	}

	if !statusOK {
		return fmt.Errorf("意外的状态码: %d，期望: %v", resp.StatusCode, serviceConfig.ExpectedStatus)
	}

	// 对于Nacos注册的服务，首先检查Docker容器是否存在且状态正常
	if ms.isNacosRegisteredService(serviceConfig) {
		if err := ms.preCheckDockerContainer(serviceConfig); err != nil {
			return fmt.Errorf("Nacos注册服务 %s 的Docker容器预检查失败: %w", serviceConfig.Name, err)
		}
	}

	return nil
}

// checkHTTPService 检查HTTP服务
func (ms *MonitorService) checkHTTPService(serviceConfig config.ServiceConfig) {
	startTime := time.Now()

	// 创建请求上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(serviceConfig.Timeout)*time.Second)
	defer cancel()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, serviceConfig.Method, serviceConfig.Endpoint, nil)
	if err != nil {
		ms.handleServiceError(serviceConfig, fmt.Sprintf("Failed to create request: %v", err))
		return
	}

	// 添加认证信息（如果有）
	if serviceConfig.Auth != nil {
		req.SetBasicAuth(serviceConfig.Auth.Username, serviceConfig.Auth.Password)
	}

	// 执行请求
	var resp *http.Response
	var requestErr error

	for attempt := 0; attempt <= serviceConfig.RetryCount; attempt++ {
		resp, requestErr = ms.httpClient.Do(req)
		if requestErr == nil {
			break
		}

		if attempt < serviceConfig.RetryCount {
			logger.Debug("HTTP请求失败，正在重试",
				"service", serviceConfig.Name,
				"attempt", attempt+1,
				"error", requestErr)
			time.Sleep(time.Second * time.Duration(attempt+1)) // 递增延迟
		}
	}

	if requestErr != nil {
		ms.handleServiceError(serviceConfig, fmt.Sprintf("HTTP request failed after %d retries: %v", serviceConfig.RetryCount+1, requestErr))
		return
	}
	defer resp.Body.Close()

	// 检查响应状态码
	statusOK := false
	for _, expectedStatus := range serviceConfig.ExpectedStatus {
		if resp.StatusCode == expectedStatus {
			statusOK = true
			break
		}
	}

	responseTime := time.Since(startTime)

	if !statusOK {
		logger.Warn("HTTP健康检查失败 - 状态码不符合预期",
			"service", serviceConfig.Name,
			"endpoint", serviceConfig.Endpoint,
			"status_code", resp.StatusCode,
			"expected", serviceConfig.ExpectedStatus,
			"response_time", responseTime)
		ms.handleServiceError(serviceConfig, fmt.Sprintf("状态码不符合预期: %d, 期望: %v", resp.StatusCode, serviceConfig.ExpectedStatus))
		return
	}

	// 服务健康
	logger.Info("HTTP健康检查成功",
		"service", serviceConfig.Name,
		"endpoint", serviceConfig.Endpoint,
		"status_code", resp.StatusCode,
		"response_time", responseTime)
	ms.handleServiceHealthy(serviceConfig, responseTime)
}

// checkHTTPServiceInstance 检查HTTP服务实例（多实例模式）
func (ms *MonitorService) checkHTTPServiceInstance(serviceConfig config.ServiceConfig) {
	startTime := time.Now()

	// 创建请求上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(serviceConfig.Timeout)*time.Second)
	defer cancel()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, serviceConfig.Method, serviceConfig.Endpoint, nil)
	if err != nil {
		ms.handleServiceInstanceError(serviceConfig, fmt.Sprintf("Failed to create request: %v", err))
		return
	}

	// 添加认证信息（如果有）
	if serviceConfig.Auth != nil {
		// 使用基本认证
		req.SetBasicAuth(serviceConfig.Auth.Username, serviceConfig.Auth.Password)
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		ms.handleServiceInstanceError(serviceConfig, fmt.Sprintf("Request failed: %v", err))
		return
	}
	defer resp.Body.Close()

	responseTime := time.Since(startTime)

	// 检查响应状态码
	statusOK := false
	for _, expectedStatus := range serviceConfig.ExpectedStatus {
		if resp.StatusCode == expectedStatus {
			statusOK = true
			break
		}
	}

	if !statusOK {
		ms.handleServiceInstanceError(serviceConfig, fmt.Sprintf("Unexpected status code: %d, expected: %v", resp.StatusCode, serviceConfig.ExpectedStatus))
		return
	}

	// 实例健康
	logger.Info("HTTP实例健康检查成功",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID,
		"endpoint", serviceConfig.Endpoint,
		"status_code", resp.StatusCode,
		"response_time", responseTime)
	ms.handleServiceInstanceHealthy(serviceConfig, responseTime)
}

// checkEMQXService 检查EMQX服务
func (ms *MonitorService) checkEMQXService(serviceConfig config.ServiceConfig) {
	// 检查EMQX配置是否存在
	if serviceConfig.EMQX == nil {
		logger.Error("EMQX服务类型需要EMQX配置", "service", serviceConfig.Name)
		return
	}

	// 获取或创建EMQX监控器
	emqxMonitor, err := ms.getOrCreateEMQXMonitor(serviceConfig)
	if err != nil {
		logger.Error("获取EMQX监控器失败",
			"service", serviceConfig.Name,
			"error", err)
		return
	}

	// 边界检查
	if len(serviceConfig.EMQX.Topics) == 0 {
		logger.Warn("EMQX服务未配置主题", "service", serviceConfig.Name)
		return
	}

	// 监控所有配置的主题
	totalTopics := len(serviceConfig.EMQX.Topics)

	logger.Info("开始EMQX主题速率监控",
		"service", serviceConfig.Name,
		"total_topics", totalTopics)

	topicMetrics, err := emqxMonitor.CheckTopicRates()
	if err != nil {
		logger.Error("EMQX主题速率检查失败",
			"service", serviceConfig.Name,
			"error", err)
		return
	}

	// 存储EMQX指标
	ms.storeEMQXMetrics(serviceConfig.Name, topicMetrics)

	// 检查告警规则（基于速率下降）
	ms.checkEMQXRateAlerts(serviceConfig, topicMetrics)

	// 检查是否需要自动解决EMQX告警
	ms.autoResolveEMQXAlerts(serviceConfig, topicMetrics)
}

// checkBusinessAPIService 检查业务API服务
func (ms *MonitorService) checkBusinessAPIService(serviceConfig config.ServiceConfig) {
	// 业务API监控可能需要特殊的检查逻辑
	// 暂时使用HTTP检查作为基础
	ms.checkHTTPService(serviceConfig)
}

// checkEMQXServiceInstance 检查EMQX服务实例（多实例模式）
func (ms *MonitorService) checkEMQXServiceInstance(serviceConfig config.ServiceConfig) {
	// 检查EMQX配置是否存在
	if serviceConfig.EMQX == nil {
		logger.Error("EMQX服务实例类型需要EMQX配置",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID)
		return
	}

	// EMQX实例监控（简化版本）
	// 这里可以添加具体的EMQX实例检查逻辑
	// 暂时假设检查成功

	// 实例健康
	logger.Info("EMQX实例健康检查成功",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID)
	ms.handleServiceInstanceHealthy(serviceConfig, 0)
}

// checkBusinessAPIServiceInstance 检查业务API服务实例（多实例模式）
func (ms *MonitorService) checkBusinessAPIServiceInstance(serviceConfig config.ServiceConfig) {
	// 业务API实例监控可能需要特殊的检查逻辑
	// 暂时使用HTTP实例检查作为基础
	ms.checkHTTPServiceInstance(serviceConfig)
}

// checkKafkaServiceInstance 检查Kafka服务实例（多实例模式）
func (ms *MonitorService) checkKafkaServiceInstance(serviceConfig config.ServiceConfig) {
	// Kafka实例监控（简化版本）
	// 这里可以添加具体的Kafka实例检查逻辑
	// 暂时假设检查成功

	// 实例健康
	logger.Info("Kafka实例健康检查成功",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID)
	ms.handleServiceInstanceHealthy(serviceConfig, 0)
}

// handleServiceHealthy 处理服务健康状态
func (ms *MonitorService) handleServiceHealthy(serviceConfig config.ServiceConfig, responseTime time.Duration) {
	// 更新服务健康状态
	serviceHealth := &model.ServiceHealth{
		ServiceName: serviceConfig.Name,
	}

	// 先尝试获取现有记录
	existing, err := ms.serviceHealthRepo.GetByServiceName(serviceConfig.Name)
	if err == nil && existing != nil {
		serviceHealth = existing
		logger.Debug("找到现有的服务健康记录",
			"service", serviceConfig.Name,
			"previous_status", existing.Status,
			"consecutive_failures", existing.ConsecutiveFailures)
	} else {
		logger.Debug("创建新的服务健康记录", "service", serviceConfig.Name)
	}

	serviceHealth.UpdateHealthy()

	if err := ms.serviceHealthRepo.CreateOrUpdate(serviceHealth); err != nil {
		logger.Error("更新服务健康状态失败", "service", serviceConfig.Name, "error", err)
	}

	// 重置容器重启计数（服务恢复健康）
	if serviceConfig.Docker != nil && ms.containerManager != nil {
		ms.containerManager.ResetRestartCount(serviceConfig.Docker.ContainerName)
		logger.Debug("容器重启计数已重置",
			"service", serviceConfig.Name,
			"container", serviceConfig.Docker.ContainerName)
	}

	// 检查是否满足自动解决告警的条件
	if ms.config.AutoResolve.Enabled && existing != nil {
		if existing.ConsecutiveSuccesses >= ms.config.AutoResolve.HealthyChecksRequired {
			ms.autoResolveServiceAlerts(serviceConfig.Name)
		} else {
			logger.Debug("连续成功检查次数不足，无法自动解决告警",
				"service", serviceConfig.Name,
				"consecutive_successes", existing.ConsecutiveSuccesses,
				"required", ms.config.AutoResolve.HealthyChecksRequired)
		}
	}

	logger.Debug("服务检查成功",
		"service", serviceConfig.Name,
		"response_time", responseTime.String(),
		"status_code", "healthy")
}

// handleServiceError 处理服务错误
func (ms *MonitorService) handleServiceError(serviceConfig config.ServiceConfig, errorMsg string) {
	// 更新服务健康状态
	serviceHealth := &model.ServiceHealth{
		ServiceName: serviceConfig.Name,
	}

	// 先尝试获取现有记录
	existing, err := ms.serviceHealthRepo.GetByServiceName(serviceConfig.Name)
	if err == nil && existing != nil {
		serviceHealth = existing
	}

	serviceHealth.UpdateUnhealthy(errorMsg)

	if err := ms.serviceHealthRepo.CreateOrUpdate(serviceHealth); err != nil {
		logger.Error("更新服务健康状态失败", "服务", serviceConfig.Name, "error", err)
	}

	logger.Warn("服务检查失败",
		"服务", serviceConfig.Name,
		"错误", errorMsg,
		"连续失败次数", serviceHealth.ConsecutiveFailures)

	// 尝试容器自愈（如果配置了Docker）
	if serviceConfig.Docker != nil && ms.containerManager != nil {
		logger.Info("尝试Docker容器自愈",
			"service", serviceConfig.Name,
			"container", serviceConfig.Docker.ContainerName)

		if err := ms.containerManager.HandleServiceFailure(serviceConfig); err != nil {
			logger.Error("容器自愈失败",
				"service", serviceConfig.Name,
				"container", serviceConfig.Docker.ContainerName,
				"error", err)
		} else {
			logger.Info("容器自愈成功完成",
				"service", serviceConfig.Name,
				"container", serviceConfig.Docker.ContainerName)
		}
	}

	// 检查是否需要触发告警
	ms.checkAlertRules(serviceConfig, serviceHealth)
}

// handleServiceInstanceHealthy 处理服务实例健康状态（多实例模式）
func (ms *MonitorService) handleServiceInstanceHealthy(serviceConfig config.ServiceConfig, responseTime time.Duration) {
	// 更新服务实例健康状态
	serviceHealth := &model.ServiceHealth{
		ServiceName: serviceConfig.Name,
		InstanceID:  serviceConfig.InstanceID,
	}

	// 先尝试获取现有记录
	existing, err := ms.serviceHealthRepo.GetByServiceNameAndInstance(serviceConfig.Name, serviceConfig.InstanceID)
	if err == nil && existing != nil {
		serviceHealth = existing
		logger.Debug("找到现有的服务实例健康记录",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID,
			"previous_status", existing.Status,
			"consecutive_failures", existing.ConsecutiveFailures)
	} else {
		logger.Debug("创建新的服务实例健康记录",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID)
	}

	serviceHealth.UpdateHealthy()

	if err := ms.serviceHealthRepo.CreateOrUpdate(serviceHealth); err != nil {
		logger.Error("更新服务实例健康状态失败",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID,
			"error", err)
	}

	// 重置容器重启计数（实例恢复健康）
	if serviceConfig.Docker != nil && ms.containerManager != nil {
		containerKey := fmt.Sprintf("%s:%s", serviceConfig.Docker.ContainerName, serviceConfig.InstanceID)
		ms.containerManager.ResetRestartCount(containerKey)
		logger.Debug("实例容器重启计数已重置",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID,
			"container", serviceConfig.Docker.ContainerName)
	}

	// 检查是否满足自动解决告警的条件
	if ms.config.AutoResolve.Enabled && existing != nil {
		if existing.ConsecutiveSuccesses >= ms.config.AutoResolve.HealthyChecksRequired {
			ms.autoResolveServiceInstanceAlerts(serviceConfig.Name, serviceConfig.InstanceID)
		}
	}

	logger.Debug("服务实例健康状态更新成功",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID,
		"status", "healthy",
		"consecutive_successes", serviceHealth.ConsecutiveSuccesses,
		"response_time", responseTime.String(),
		"status_code", "healthy")
}

// handleServiceInstanceError 处理服务实例错误（多实例模式）
func (ms *MonitorService) handleServiceInstanceError(serviceConfig config.ServiceConfig, errorMsg string) {
	// 更新服务实例健康状态
	serviceHealth := &model.ServiceHealth{
		ServiceName: serviceConfig.Name,
		InstanceID:  serviceConfig.InstanceID,
	}

	// 先尝试获取现有记录
	existing, err := ms.serviceHealthRepo.GetByServiceNameAndInstance(serviceConfig.Name, serviceConfig.InstanceID)
	if err == nil && existing != nil {
		serviceHealth = existing
	}

	serviceHealth.UpdateUnhealthy(errorMsg)

	if err := ms.serviceHealthRepo.CreateOrUpdate(serviceHealth); err != nil {
		logger.Error("更新服务实例健康状态失败",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID,
			"error", err)
	}

	logger.Warn("服务实例检查失败",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID,
		"error", errorMsg,
		"consecutive_failures", serviceHealth.ConsecutiveFailures)

	// 尝试容器自愈（如果配置了Docker）
	if serviceConfig.Docker != nil && ms.containerManager != nil {
		logger.Info("尝试Docker容器实例自愈",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID,
			"container", serviceConfig.Docker.ContainerName)

		if err := ms.containerManager.HandleServiceFailure(serviceConfig); err != nil {
			logger.Error("容器实例自愈失败",
				"service", serviceConfig.Name,
				"instance_id", serviceConfig.InstanceID,
				"container", serviceConfig.Docker.ContainerName,
				"error", err)
		} else {
			logger.Info("容器实例自愈成功完成",
				"service", serviceConfig.Name,
				"instance_id", serviceConfig.InstanceID,
				"container", serviceConfig.Docker.ContainerName)
		}
	}

	// 检查是否需要触发告警
	ms.checkAlertRulesForInstance(serviceConfig, serviceHealth)
}

// handleServiceWarning 处理服务警告
func (ms *MonitorService) handleServiceWarning(serviceConfig config.ServiceConfig, warningMsg string) {
	logger.Warn("服务警告", "service", serviceConfig.Name, "warning", warningMsg)
	// 这里可以添加警告级别的告警逻辑
}

// checkAlertRules 检查告警规则
func (ms *MonitorService) checkAlertRules(serviceConfig config.ServiceConfig, serviceHealth *model.ServiceHealth) {
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		// 检查服务是否在规则范围内
		serviceInRule := false
		for _, serviceName := range rule.Services {
			if serviceName == serviceConfig.Name {
				serviceInRule = true
				break
			}
		}

		if !serviceInRule {
			continue
		}

		// 检查告警条件
		if ms.shouldTriggerAlert(rule, serviceHealth) {
			ms.createAlert(rule, serviceConfig, serviceHealth)
		}
	}
}

// shouldTriggerAlert 判断是否应该触发告警
func (ms *MonitorService) shouldTriggerAlert(rule config.AlertRuleConfig, serviceHealth *model.ServiceHealth) bool {
	switch rule.Condition.Type {
	case "consecutive_failures":
		if threshold, ok := rule.Condition.Threshold.(int); ok {
			return serviceHealth.ConsecutiveFailures >= threshold
		}
		if threshold, ok := rule.Condition.Threshold.(float64); ok {
			return serviceHealth.ConsecutiveFailures >= int(threshold)
		}
	case "emqx_rate_drop":
		return ms.checkEMQXRate(rule, serviceHealth)
	}
	return false
}

// checkEMQXRate 检查EMQX速率下降告警条件
func (ms *MonitorService) checkEMQXRate(rule config.AlertRuleConfig, serviceHealth *model.ServiceHealth) bool {
	// 获取阈值配置
	var thresholdPct float64
	if threshold, ok := rule.Condition.Threshold.(int); ok {
		thresholdPct = float64(threshold)
	} else if threshold, ok := rule.Condition.Threshold.(float64); ok {
		thresholdPct = threshold
	} else {
		logger.Error("EMQX速率下降告警的阈值类型无效",
			"rule", rule.Name,
			"threshold", rule.Condition.Threshold)
		return false
	}

	// 获取最新的EMQX速率下降指标
	now := time.Now()
	startTime := now.Add(-5 * time.Minute) // 查询最近5分钟的数据

	metrics, err := ms.metricsRepo.GetByTimeRange(
		serviceHealth.ServiceName,
		model.MetricEMQXRate,
		startTime,
		now,
	)
	if err != nil {
		logger.Error("获取EMQX速率下降指标失败",
			"service", serviceHealth.ServiceName,
			"error", err)
		return false
	}

	if len(metrics) == 0 {
		logger.Debug("未找到EMQX速率下降指标",
			"service", serviceHealth.ServiceName)
		return false
	}

	// 检查最新的速率下降数据
	latestMetric := metrics[len(metrics)-1]
	currentDropPct := latestMetric.MetricValue

	logger.Debug("EMQX速率下降检查",
		"service", serviceHealth.ServiceName,
		"current_remaining_pct", currentDropPct,
		"threshold_pct", thresholdPct,
		"rule", rule.Name)

	// 如果当前速率占基线的百分比低于阈值，触发告警
	return currentDropPct <= thresholdPct
}

// createAlert 创建告警
func (ms *MonitorService) createAlert(rule config.AlertRuleConfig, serviceConfig config.ServiceConfig, serviceHealth *model.ServiceHealth) {
	// 检查是否已存在相同的活跃告警
	existingAlert, err := ms.alertRepo.FindExistingAlert(rule.Name, serviceConfig.Name)
	if err != nil {
		logger.Error("检查现有告警失败", "error", err)
		return
	}

	if existingAlert != nil {
		logger.Debug("告警已存在", "rule", rule.Name, "service", serviceConfig.Name)
		return
	}

	// 创建新告警
	alert := &model.Alert{
		RuleName:    rule.Name,
		ServiceName: serviceConfig.Name,
		Status:      model.AlertStatusFiring,
		Message:     fmt.Sprintf("服务 %s 健康检测失败，检测次数: %d", serviceConfig.Name, serviceHealth.ConsecutiveFailures),
		Severity:    model.AlertSeverity(rule.Severity),
		StartedAt:   time.Now(),
		Metadata:    fmt.Sprintf(`{"endpoint":"%s","consecutive_failures":%d}`, serviceConfig.Endpoint, serviceHealth.ConsecutiveFailures),
	}

	if err := ms.alertRepo.Create(alert); err != nil {
		logger.Error("创建告警失败", "error", err)
		return
	}

	logger.Info("告警已创建",
		"规则", rule.Name,
		"服务", serviceConfig.Name,
		"严重程度", rule.Severity,
		"告警ID", alert.ID)

	// 检查是否需要触发容器重启
	if rule.AutoRestartContainer && serviceConfig.Docker != nil {
		ms.handleAlertContainerRestart(rule, serviceConfig, alert)
	}

	// 异步发送通知
	if ms.notificationService != nil {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					logger.Error("告警通知发送时发生panic",
						"告警ID", alert.ID,
						"panic", r)
				}
			}()

			if err := ms.notificationService.SendAlertNotifications(alert); err != nil {
				logger.Error("发送告警通知失败",
					"告警ID", alert.ID,
					"服务", serviceConfig.Name,
					"error", err)
			} else {
				logger.Info("告警通知发送成功",
					"告警ID", alert.ID,
					"服务", serviceConfig.Name)
			}
		}()
	}
}

// handleAlertContainerRestart 通用的告警容器重启处理方法
func (ms *MonitorService) handleAlertContainerRestart(rule config.AlertRuleConfig, serviceConfig config.ServiceConfig, alert *model.Alert) {
	logger.Info("处理告警容器重启",
		"服务", serviceConfig.Name,
		"告警ID", alert.ID,
		"规则", alert.RuleName)

	// 检查是否配置了Docker容器重启
	if serviceConfig.Docker == nil {
		logger.Debug("服务未找到Docker配置，跳过容器重启",
			"服务", serviceConfig.Name)
		return
	}

	// 检查容器管理器是否可用
	if ms.containerManager == nil {
		logger.Error("容器管理器不可用，无法处理告警容器重启",
			"服务", serviceConfig.Name,
			"告警ID", alert.ID)
		return
	}

	// 确定要重启的容器名称
	containerName := rule.RestartContainerName
	if containerName == "" {
		// 如果告警规则中没有指定容器名称，使用服务配置中的默认容器名称
		containerName = serviceConfig.Docker.ContainerName
	}

	if containerName == "" {
		logger.Error("无法确定要重启的容器名称",
			"服务", serviceConfig.Name,
			"告警ID", alert.ID)
		ms.updateAlertMetadataWithRestartResult(alert, false, "无法确定要重启的容器名称")
		return
	}

	// 触发容器重启
	logger.Info("为告警触发容器重启",
		"服务", serviceConfig.Name,
		"容器", containerName,
		"告警ID", alert.ID,
		"规则", alert.RuleName)

	// 创建临时的服务配置，使用指定的容器名称
	tempServiceConfig := serviceConfig
	if rule.RestartContainerName != "" {
		// 如果告警规则指定了容器名称，需要创建一个临时的Docker配置
		tempDockerConfig := *serviceConfig.Docker
		tempDockerConfig.ContainerName = containerName
		tempServiceConfig.Docker = &tempDockerConfig
	}

	if err := ms.containerManager.HandleServiceFailure(tempServiceConfig); err != nil {
		logger.Error("告警容器重启失败",
			"服务", serviceConfig.Name,
			"容器", containerName,
			"告警ID", alert.ID,
			"规则", alert.RuleName,
			"error", err)

		// 更新告警元数据，记录重启失败信息
		ms.updateAlertMetadataWithRestartResult(alert, false, err.Error())
	} else {
		logger.Info("告警容器重启成功完成",
			"服务", serviceConfig.Name,
			"容器", containerName,
			"告警ID", alert.ID,
			"规则", alert.RuleName)

		// 更新告警元数据，记录重启成功信息
		ms.updateAlertMetadataWithRestartResult(alert, true, "")
	}
}

// updateAlertMetadataWithRestartResult 更新告警元数据，记录重启结果
func (ms *MonitorService) updateAlertMetadataWithRestartResult(alert *model.Alert, success bool, errorMsg string) {
	// 解析现有的元数据
	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(alert.Metadata), &metadata); err != nil {
		logger.Error("解析告警元数据失败", "告警ID", alert.ID, "error", err)
		metadata = make(map[string]interface{})
	}

	// 添加重启信息
	restartInfo := map[string]interface{}{
		"attempted_at": time.Now().Format(time.RFC3339),
		"success":      success,
	}

	if !success && errorMsg != "" {
		restartInfo["error"] = errorMsg
	}

	metadata["container_restart"] = restartInfo

	// 序列化更新后的元数据
	updatedMetadata, err := json.Marshal(metadata)
	if err != nil {
		logger.Error("序列化更新后的元数据失败", "alert_id", alert.ID, "error", err)
		return
	}

	// 更新告警记录
	alert.Metadata = string(updatedMetadata)
	if err := ms.alertRepo.Update(alert); err != nil {
		logger.Error("更新告警元数据失败", "alert_id", alert.ID, "error", err)
	} else {
		logger.Debug("告警元数据已更新重启结果",
			"alert_id", alert.ID,
			"restart_success", success)
	}
}

// preCheckKafkaService Kafka服务预检查
func (ms *MonitorService) preCheckKafkaService(serviceConfig config.ServiceConfig) error {
	if serviceConfig.Kafka == nil {
		return fmt.Errorf("Kafka服务类型需要Kafka配置")
	}

	// 验证Kafka配置
	if len(serviceConfig.Kafka.Brokers) == 0 {
		return fmt.Errorf("Kafka brokers不能为空")
	}

	if len(serviceConfig.Kafka.ConsumerGroups) == 0 {
		return fmt.Errorf("Kafka消费者组不能为空")
	}

	// 尝试创建Kafka监控器进行连接测试
	kafkaMonitor, err := NewKafkaMonitor(serviceConfig.Kafka)
	if err != nil {
		return fmt.Errorf("创建Kafka监控器失败: %w", err)
	}
	defer kafkaMonitor.Close()

	logger.Info("Kafka服务预检查成功",
		"service", serviceConfig.Name,
		"brokers", serviceConfig.Kafka.Brokers,
		"consumer_groups", serviceConfig.Kafka.ConsumerGroups)

	return nil
}

// preCheckEMQXService EMQX服务预检查
func (ms *MonitorService) preCheckEMQXService(serviceConfig config.ServiceConfig) error {
	// 检查EMQX配置是否存在
	if serviceConfig.EMQX == nil {
		return fmt.Errorf("EMQX服务类型需要EMQX配置")
	}

	// 验证EMQX配置
	if serviceConfig.EMQX.Host == "" {
		return fmt.Errorf("EMQX主机不能为空")
	}

	if serviceConfig.EMQX.Port == 0 {
		return fmt.Errorf("EMQX端口不能为零")
	}

	if len(serviceConfig.EMQX.Topics) == 0 {
		return fmt.Errorf("EMQX主题不能为空")
	}

	// 尝试创建EMQX监控器进行连接测试，并将其存储到监控器映射中供后续使用
	emqxMonitor, err := NewEMQXMonitor(serviceConfig.Name, serviceConfig.EMQX, ms.metricsRepo)
	if err != nil {
		return fmt.Errorf("创建EMQX监控器失败: %w", err)
	}

	// 将监控器存储到映射中，避免重复创建
	ms.mu.Lock()
	ms.emqxMonitors[serviceConfig.Name] = emqxMonitor
	ms.mu.Unlock()

	logger.Info("EMQX服务预检查成功",
		"service", serviceConfig.Name,
		"host", serviceConfig.EMQX.Host,
		"port", serviceConfig.EMQX.Port,
		"topics", serviceConfig.EMQX.Topics)

	return nil
}

// isNacosRegisteredService 判断服务是否是Nacos注册的服务
func (ms *MonitorService) isNacosRegisteredService(serviceConfig config.ServiceConfig) bool {
	// 通过以下条件判断服务是否来自Nacos：
	// 1. 服务标记为自动发现的服务 (IsDiscovered=true)
	// 2. 服务有NacosIP字段（表示从Nacos获取了IP信息）
	// 3. 服务有Name但原始配置中没有Endpoint（需要从Nacos解析）
	return serviceConfig.IsDiscovered || serviceConfig.NacosIP != ""
}

// preCheckDockerContainer 预检查Docker容器是否存在且状态正常
func (ms *MonitorService) preCheckDockerContainer(serviceConfig config.ServiceConfig) error {
	// 检查是否配置了Docker
	if serviceConfig.Docker == nil {
		return nil // 没有Docker配置，跳过容器检查
	}

	// 检查是否配置了Agent
	if serviceConfig.Docker.Agent == nil {
		return fmt.Errorf("服务 %s 配置了Docker但缺少Agent配置", serviceConfig.Name)
	}

	// 检查容器名称是否配置
	if serviceConfig.Docker.ContainerName == "" {
		return fmt.Errorf("服务 %s 的Docker容器名称不能为空", serviceConfig.Name)
	}

	logger.Debug("开始预检查Docker容器",
		"service", serviceConfig.Name,
		"container", serviceConfig.Docker.ContainerName,
		"agent", fmt.Sprintf("%s:%d", serviceConfig.Docker.Agent.Host, serviceConfig.Docker.Agent.Port))

	// 通过ContainerManager获取容器状态
	if ms.containerManager == nil {
		return fmt.Errorf("容器管理器不可用，无法检查容器状态")
	}

	status, err := ms.containerManager.GetContainerStatus(serviceConfig)
	if err != nil {
		return fmt.Errorf("获取容器 %s 状态失败: %w", serviceConfig.Docker.ContainerName, err)
	}

	// 检查容器是否健康（运行中）
	if !status.IsHealthy() {
		return fmt.Errorf("容器 %s 状态异常: state=%s, status=%s",
			serviceConfig.Docker.ContainerName, status.State, status.Status)
	}

	logger.Info("Docker容器预检查通过",
		"service", serviceConfig.Name,
		"container", serviceConfig.Docker.ContainerName,
		"state", status.State,
		"status", status.Status)

	return nil
}

// checkKafkaService 检查Kafka消费者组延迟
func (ms *MonitorService) checkKafkaService(serviceConfig config.ServiceConfig) {
	// 获取或创建Kafka监控器
	kafkaMonitor, err := ms.getOrCreateKafkaMonitor(serviceConfig)
	if err != nil {
		logger.Error("获取Kafka监控器失败",
			"service", serviceConfig.Name,
			"error", err)
		return
	}

	// 边界检查
	if len(serviceConfig.Kafka.ConsumerGroups) == 0 {
		logger.Warn("Kafka服务未配置消费者组", "service", serviceConfig.Name)
		return
	}

	for _, consumerGroup := range serviceConfig.Kafka.ConsumerGroups {
		groupMetrics, err := kafkaMonitor.CheckConsumerGroupLagWithRetry(consumerGroup)
		if err != nil {
			logger.Error("Kafka消费者组延迟检查失败",
				"service", serviceConfig.Name,
				"consumer_group", consumerGroup,
				"error", err)
			continue
		}

		// 存储消费者组指标（主题级别和分区级别延迟）
		ms.storeKafkaMetrics(serviceConfig.Name, consumerGroup, groupMetrics)

		// 检查告警规则（基于主题级别延迟）
		ms.checkConsumerGroupAlerts(serviceConfig, consumerGroup, groupMetrics)

		// 检查是否需要自动解决Kafka告警
		ms.autoResolveKafkaAlerts(serviceConfig, consumerGroup, groupMetrics)

	}

}

// getOrCreateKafkaMonitor 获取或创建Kafka监控器
func (ms *MonitorService) getOrCreateKafkaMonitor(serviceConfig config.ServiceConfig) (*KafkaMonitor, error) {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	// 检查是否已存在
	if monitor, exists := ms.kafkaMonitors[serviceConfig.Name]; exists {
		if monitor.IsConnected() {
			return monitor, nil
		}
		// 如果连接已断开，关闭旧的监控器
		if err := monitor.Close(); err != nil {
			logger.Warn("关闭断开的Kafka监控器时出错",
				"service", serviceConfig.Name,
				"error", err)
		}
		delete(ms.kafkaMonitors, serviceConfig.Name)
	}

	// 创建新的监控器
	kafkaMonitor, err := NewKafkaMonitor(serviceConfig.Kafka)
	if err != nil {
		return nil, fmt.Errorf("创建Kafka监控器失败: %w", err)
	}

	ms.kafkaMonitors[serviceConfig.Name] = kafkaMonitor
	logger.Info("Kafka监控器已创建",
		"service", serviceConfig.Name,
		"brokers", serviceConfig.Kafka.Brokers)

	return kafkaMonitor, nil
}

// storeKafkaMetrics 存储Kafka指标数据
func (ms *MonitorService) storeKafkaMetrics(serviceName, consumerGroup string, groupMetrics *ConsumerGroupMetrics) {
	now := time.Now()

	// 创建标签信息（用于消费者组级别的指标）
	groupTags := fmt.Sprintf(`{"consumer_group":"%s"}`, consumerGroup)

	// 存储每个主题的延迟指标
	for topic, topicMetrics := range groupMetrics.TopicMetrics {
		topicTags := fmt.Sprintf(`{"consumer_group":"%s","topic":"%s"}`,
			consumerGroup, topic)

		// 存储主题级别的消费延迟（消费者组消费单个主题的延迟）
		if err := ms.metricsRepo.Create(&model.MetricsHistory{
			ServiceName: serviceName,
			MetricName:  model.MetricTopicLag,
			MetricValue: float64(topicMetrics.TopicLag),
			Tags:        topicTags,
			CollectedAt: now,
		}); err != nil {
			logger.Error("存储主题延迟指标失败", "error", err)
		}

		// 存储分区级别的延迟
		for partition, partitionLag := range topicMetrics.PartitionLags {
			partitionTags := fmt.Sprintf(`{"consumer_group":"%s","topic":"%s","partition":%d}`,
				consumerGroup, topic, partition)

			if err := ms.metricsRepo.Create(&model.MetricsHistory{
				ServiceName: serviceName,
				MetricName:  model.MetricPartitionLag,
				MetricValue: float64(partitionLag),
				Tags:        partitionTags,
				CollectedAt: now,
			}); err != nil {
				logger.Error("存储分区延迟指标失败", "error", err)
			}
		}
	}

	// 存储消费者组总延迟（所有主题的总和）
	if err := ms.metricsRepo.Create(&model.MetricsHistory{
		ServiceName: serviceName,
		MetricName:  model.MetricTotalLag,
		MetricValue: float64(groupMetrics.TotalLag),
		Tags:        groupTags,
		CollectedAt: now,
	}); err != nil {
		logger.Error("存储总延迟指标失败", "error", err)
	}

	// 存储消费者组级别的统计指标
	// 存储主题数量
	if err := ms.metricsRepo.Create(&model.MetricsHistory{
		ServiceName: serviceName,
		MetricName:  model.MetricTopicCount,
		MetricValue: float64(groupMetrics.TopicCount),
		Tags:        groupTags,
		CollectedAt: now,
	}); err != nil {
		logger.Error("存储主题数量指标失败", "error", err)
	}

	// 存储连接状态
	connectivityValue := 0.0
	if groupMetrics.ConnectivityOK {
		connectivityValue = 1.0
	}
	if err := ms.metricsRepo.Create(&model.MetricsHistory{
		ServiceName: serviceName,
		MetricName:  model.MetricKafkaConnectivity,
		MetricValue: connectivityValue,
		Tags:        groupTags,
		CollectedAt: now,
	}); err != nil {
		logger.Error("存储连接性指标失败", "error", err)
	}
}

// getOrCreateEMQXMonitor 获取或创建EMQX监控器
func (ms *MonitorService) getOrCreateEMQXMonitor(serviceConfig config.ServiceConfig) (*EMQXMonitor, error) {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	// 检查是否已存在
	if monitor, exists := ms.emqxMonitors[serviceConfig.Name]; exists {
		// 直接返回现有监控器，让CheckTopicRates方法处理重连逻辑
		return monitor, nil
	}

	// 创建新的监控器
	emqxMonitor, err := NewEMQXMonitor(serviceConfig.Name, serviceConfig.EMQX, ms.metricsRepo)
	if err != nil {
		return nil, fmt.Errorf("创建EMQX监控器失败: %w", err)
	}

	ms.emqxMonitors[serviceConfig.Name] = emqxMonitor
	logger.Info("EMQX监控器已创建",
		"service", serviceConfig.Name,
		"host", serviceConfig.EMQX.Host,
		"port", serviceConfig.EMQX.Port,
		"topics", serviceConfig.EMQX.Topics)

	return emqxMonitor, nil
}

// storeEMQXMetrics 存储EMQX指标数据
func (ms *MonitorService) storeEMQXMetrics(serviceName string, topicMetrics []*TopicRateMetrics) {
	now := time.Now()

	// 存储每个主题的速率指标
	for _, topicMetric := range topicMetrics {
		topicTags := fmt.Sprintf(`{"topic":"%s"}`, topicMetric.Topic)

		// 存储当前速率
		if err := ms.metricsRepo.Create(&model.MetricsHistory{
			ServiceName: serviceName,
			MetricName:  model.MetricEMQXCurrentRate,
			MetricValue: topicMetric.CurrentRate,
			Tags:        topicTags,
			CollectedAt: now,
		}); err != nil {
			logger.Error("存储EMQX当前速率指标失败", "error", err)
		}

		// 存储基线速率
		if err := ms.metricsRepo.Create(&model.MetricsHistory{
			ServiceName: serviceName,
			MetricName:  model.MetricEMQXBaseline,
			MetricValue: topicMetric.BaselineRate,
			Tags:        topicTags,
			CollectedAt: now,
		}); err != nil {
			logger.Error("存储EMQX基线指标失败", "error", err)
		}

		// 存储速率下降百分比
		if err := ms.metricsRepo.Create(&model.MetricsHistory{
			ServiceName: serviceName,
			MetricName:  model.MetricEMQXRate,
			MetricValue: topicMetric.RatePct,
			Tags:        topicTags,
			CollectedAt: now,
		}); err != nil {
			logger.Error("存储EMQX速率下降指标失败", "error", err)
		}

		// 存储消息计数指标
		if err := ms.metricsRepo.Create(&model.MetricsHistory{
			ServiceName: serviceName,
			MetricName:  model.MetricMessageRate,
			MetricValue: float64(topicMetric.MessageIn),
			Tags:        topicTags,
			CollectedAt: now,
		}); err != nil {
			logger.Error("存储EMQX消息计数指标失败", "error", err)
		}

		// 存储连接状态
		connectivityValue := 0.0
		if topicMetric.ConnectivityOK {
			connectivityValue = 1.0
		}
		if err := ms.metricsRepo.Create(&model.MetricsHistory{
			ServiceName: serviceName,
			MetricName:  model.MetricEMQXConnectivity,
			MetricValue: connectivityValue,
			Tags:        topicTags,
			CollectedAt: now,
		}); err != nil {
			logger.Error("存储EMQX连接性指标失败", "error", err)
		}
	}
}

// checkEMQXRateAlerts 检查EMQX速率告警
func (ms *MonitorService) checkEMQXRateAlerts(serviceConfig config.ServiceConfig, topicMetrics []*TopicRateMetrics) {
	if ms.config.AlertRules == nil {
		return
	}

	// 告警抑制：如果服务启动时间未超过配置的rate_window，则不进行告警处理
	if serviceConfig.EMQX != nil && serviceConfig.EMQX.RateWindow > 0 {
		timeSinceStart := time.Since(ms.startTime)
		rateWindowDuration := time.Duration(serviceConfig.EMQX.RateWindow) * time.Second

		if timeSinceStart < rateWindowDuration {
			logger.Debug("EMQX告警抑制中：服务启动时间未超过rate_window",
				"service", serviceConfig.Name,
				"time_since_start", timeSinceStart.String(),
				"rate_window", rateWindowDuration.String(),
				"remaining_time", (rateWindowDuration - timeSinceStart).String())
			return
		}
	}

	for _, rule := range ms.config.AlertRules {
		// 检查规则是否适用于当前服务
		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		// 检查EMQX速率下降告警
		if rule.Condition.Type == "emqx_rate_drop" {
			// 获取阈值配置
			var thresholdPct float64
			if threshold, ok := rule.Condition.Threshold.(int); ok {
				thresholdPct = float64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(float64); ok {
				thresholdPct = threshold
			} else {
				logger.Error("EMQX速率下降告警的阈值类型无效",
					"rule", rule.Name,
					"threshold", rule.Condition.Threshold)
				continue
			}

			// 检查每个主题的速率下降
			for _, metric := range topicMetrics {
				if metric.RatePct <= thresholdPct {
					// 构造唯一的告警标识，包含主题信息
					alertKey := fmt.Sprintf("%s-%s", rule.Name, metric.Topic)

					// 检查是否已存在相同的活跃告警
					existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceConfig.Name)
					if err != nil {
						logger.Error("检查现有告警失败", "error", err)
						continue
					}
					if existingAlert != nil {
						logger.Debug("EMQX速率下降告警已存在",
							"rule", alertKey,
							"service", serviceConfig.Name,
							"topic", metric.Topic)
						continue // 已存在活跃告警，跳过
					}

					alertMessage := fmt.Sprintf("EMQX主题：%s，当前速率 %.1f%% 低于阈值 %.1f%%",
						metric.Topic, metric.RatePct, thresholdPct)

					// 创建 metadata JSON
					metadata := fmt.Sprintf(`{"topic":"%s","current_rate":%.2f,"baseline_rate":%.2f,"rate_remaining_pct":%.2f,"threshold":%.1f}`,
						metric.Topic, metric.CurrentRate, metric.BaselineRate, metric.RatePct, thresholdPct)

					alert := &model.Alert{
						ServiceName: serviceConfig.Name,
						RuleName:    alertKey, // 使用包含主题的唯一规则名
						Severity:    model.AlertSeverity(rule.Severity),
						Message:     alertMessage,
						Status:      model.AlertStatusFiring,
						StartedAt:   time.Now(),
						Metadata:    metadata,
					}

					if err := ms.alertRepo.Create(alert); err != nil {
						logger.Error("创建EMQX速率下降告警失败", "error", err)
						continue
					}

					logger.Info("EMQX速率下降告警已创建",
						"rule", alertKey,
						"service", serviceConfig.Name,
						"topic", metric.Topic,
						"current_rate", metric.CurrentRate,
						"baseline_rate", metric.BaselineRate,
						"remaining_percentage", metric.RatePct,
						"threshold", thresholdPct,
						"severity", rule.Severity)

					// 检查是否需要触发容器重启
					if rule.AutoRestartContainer && serviceConfig.Docker != nil {
						ms.handleAlertContainerRestart(rule, serviceConfig, alert)
					}

					// 发送通知
					if ms.notificationService != nil {
						if err := ms.notificationService.SendAlertNotifications(alert); err != nil {
							logger.Error("发送EMQX速率下降告警通知失败", "error", err)
						}
					}
				}
			}
		}
	}
}

// checkConsumerGroupAlerts 检查单个消费者组的告警规则
func (ms *MonitorService) checkConsumerGroupAlerts(serviceConfig config.ServiceConfig, consumerGroup string, groupMetrics *ConsumerGroupMetrics) {
	if ms.config.AlertRules == nil {
		return
	}

	for _, rule := range ms.config.AlertRules {
		// 检查规则是否适用于当前服务
		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		// 检查主题级别消费延迟阈值（消费者组消费单个主题的延迟）
		if rule.Condition.Type == "topic_lag" {
			// 获取阈值配置
			var thresholdValue int64
			if threshold, ok := rule.Condition.Threshold.(int); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(float64); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(int64); ok {
				thresholdValue = threshold
			} else {
				logger.Error("主题延迟告警的阈值类型无效",
					"rule", rule.Name,
					"threshold", rule.Condition.Threshold)
				continue
			}

			if thresholdValue > 0 {
				// 检查每个主题的延迟
				for topic, topicMetrics := range groupMetrics.TopicMetrics {
					if topicMetrics.TopicLag > thresholdValue {
						// 构造唯一的告警标识，包含消费者组和主题信息
						alertKey := fmt.Sprintf("%s-%s-%s", rule.Name, consumerGroup, topic)

						// 检查是否已存在相同的活跃告警
						existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceConfig.Name)
						if err != nil {
							logger.Error("检查现有告警失败", "error", err)
							continue
						}
						if existingAlert != nil {
							logger.Debug("主题延迟告警已存在",
								"rule", alertKey,
								"service", serviceConfig.Name,
								"consumer_group", consumerGroup,
								"topic", topic)
							continue // 已存在活跃告警，跳过
						}

						alertMessage := fmt.Sprintf("消费者组：%s，主题延迟 %d 超过阈值 %d (主题：%s)",
							consumerGroup, topicMetrics.TopicLag, thresholdValue, topic)

						// 创建 metadata JSON
						metadata := fmt.Sprintf(`{"consumer_group":"%s","topic_lag":%d,"topic":"%s","threshold":%d}`,
							consumerGroup, topicMetrics.TopicLag, topic, thresholdValue)

						alert := &model.Alert{
							ServiceName: serviceConfig.Name,
							RuleName:    alertKey, // 使用包含消费者组和主题的唯一规则名
							Severity:    model.AlertSeverity(rule.Severity),
							Message:     alertMessage,
							Status:      model.AlertStatusFiring,
							StartedAt:   time.Now(),
							Metadata:    metadata,
						}

						if err := ms.alertRepo.Create(alert); err != nil {
							logger.Error("创建Kafka主题延迟告警失败", "error", err)
							continue
						}

						logger.Warn("Kafka主题延迟告警触发",
							"service", serviceConfig.Name,
							"consumer_group", consumerGroup,
							"rule", rule.Name,
							"alert_key", alertKey,
							"severity", rule.Severity,
							"topic_lag", topicMetrics.TopicLag,
							"topic", topic,
							"threshold", thresholdValue)

						// 检查是否需要触发容器重启
						if rule.AutoRestartContainer && serviceConfig.Docker != nil {
							ms.handleAlertContainerRestart(rule, serviceConfig, alert)
						}

						// 异步发送通知
						if ms.notificationService != nil {
							go func(alertToSend *model.Alert) {
								defer func() {
									if r := recover(); r != nil {
										logger.Error("Kafka主题延迟告警通知发送时发生panic",
											"alert_id", alertToSend.ID,
											"panic", r)
									}
								}()

								if err := ms.notificationService.SendAlertNotifications(alertToSend); err != nil {
									logger.Error("发送Kafka主题延迟告警通知失败",
										"alert_id", alertToSend.ID,
										"service", serviceConfig.Name,
										"consumer_group", consumerGroup,
										"topic", topic,
										"error", err)
								} else {
									logger.Info("Kafka主题延迟告警通知发送成功",
										"alert_id", alertToSend.ID,
										"service", serviceConfig.Name,
										"consumer_group", consumerGroup,
										"topic", topic)
								}
							}(alert)
						}
					}
				}
			}
		}

		// 检查分区延迟阈值
		if rule.Condition.Type == "partition_lag" {
			// 获取阈值配置
			var thresholdValue int64
			if threshold, ok := rule.Condition.Threshold.(int); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(float64); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(int64); ok {
				thresholdValue = threshold
			} else {
				logger.Error("分区延迟告警的阈值类型无效",
					"rule", rule.Name,
					"threshold", rule.Condition.Threshold)
				continue
			}

			if thresholdValue > 0 {
				// 检查所有分区的延迟
				for topic, topicMetrics := range groupMetrics.TopicMetrics {
					for partition, lag := range topicMetrics.PartitionLags {
						if lag > thresholdValue {
							// 构造唯一的告警标识，包含消费者组和分区信息
							alertKey := fmt.Sprintf("%s-%s-%s-%d", rule.Name, consumerGroup, topic, partition)

							// 检查是否已存在相同的活跃告警
							existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceConfig.Name)
							if err != nil {
								logger.Error("检查现有告警失败", "error", err)
								continue
							}
							if existingAlert != nil {
								logger.Debug("分区延迟告警已存在",
									"rule", alertKey,
									"service", serviceConfig.Name,
									"consumer_group", consumerGroup,
									"topic", topic,
									"partition", partition)
								continue // 已存在活跃告警，跳过
							}

							alertMessage := fmt.Sprintf("消费者组：%s，分区延迟 %d 超过阈值 %d (主题：%s，分区：%d)",
								consumerGroup, lag, thresholdValue, topic, partition)

							// 创建 metadata JSON
							metadata := fmt.Sprintf(`{"consumer_group":"%s","partition_lag":%d,"topic":"%s","partition":%d,"threshold":%d}`,
								consumerGroup, lag, topic, partition, thresholdValue)

							alert := &model.Alert{
								ServiceName: serviceConfig.Name,
								RuleName:    alertKey, // 使用包含消费者组和分区的唯一规则名
								Severity:    model.AlertSeverity(rule.Severity),
								Message:     alertMessage,
								Status:      model.AlertStatusFiring,
								StartedAt:   time.Now(),
								Metadata:    metadata,
							}

							if err := ms.alertRepo.Create(alert); err != nil {
								logger.Error("创建Kafka分区延迟告警失败", "error", err)
								continue
							}

							logger.Warn("Kafka分区延迟告警触发",
								"service", serviceConfig.Name,
								"consumer_group", consumerGroup,
								"rule", rule.Name,
								"alert_key", alertKey,
								"severity", rule.Severity,
								"partition_lag", lag,
								"topic", topic,
								"partition", partition,
								"threshold", thresholdValue)

							// 检查是否需要触发容器重启
							if rule.AutoRestartContainer && serviceConfig.Docker != nil {
								ms.handleAlertContainerRestart(rule, serviceConfig, alert)
							}

							// 异步发送通知
							if ms.notificationService != nil {
								go func(alertToSend *model.Alert) {
									defer func() {
										if r := recover(); r != nil {
											logger.Error("Kafka分区延迟告警通知发送时发生panic",
												"alert_id", alertToSend.ID,
												"panic", r)
										}
									}()

									if err := ms.notificationService.SendAlertNotifications(alertToSend); err != nil {
										logger.Error("发送Kafka分区延迟告警通知失败",
											"alert_id", alertToSend.ID,
											"service", serviceConfig.Name,
											"consumer_group", consumerGroup,
											"topic", topic,
											"partition", partition,
											"error", err)
									} else {
										logger.Info("Kafka分区延迟告警通知发送成功",
											"alert_id", alertToSend.ID,
											"service", serviceConfig.Name,
											"consumer_group", consumerGroup,
											"topic", topic,
											"partition", partition)
									}
								}(alert)
							}
						}
					}
				}
			}
		}
	}
}

// isRuleApplicableToService 检查告警规则是否适用于指定服务
func (ms *MonitorService) isRuleApplicableToService(rule config.AlertRuleConfig, serviceName string) bool {
	if !rule.Enabled {
		return false
	}

	// 如果规则没有指定服务列表，则适用于所有服务
	if len(rule.Services) == 0 {
		return true
	}

	// 检查服务名是否在规则的服务列表中
	for _, ruleService := range rule.Services {
		if ruleService == serviceName {
			return true
		}
	}

	return false
}

// autoResolveServiceAlerts 自动解决服务的活跃告警
func (ms *MonitorService) autoResolveServiceAlerts(serviceName string) {
	// 获取该服务的所有活跃告警
	activeAlerts, err := ms.alertRepo.GetActiveAlertsByService(serviceName)
	if err != nil {
		logger.Error("获取活跃告警进行自动解决时失败",
			"service", serviceName,
			"error", err)
		return
	}

	if len(activeAlerts) == 0 {
		logger.Debug("没有活跃告警需要解决", "service", serviceName)
		return
	}

	logger.Info("为恢复的服务自动解决告警",
		"service", serviceName,
		"alert_count", len(activeAlerts))

	// 逐个解决告警
	for _, alert := range activeAlerts {
		// 更新告警状态为已解决
		alert.Resolve()

		if err := ms.alertRepo.Update(&alert); err != nil {
			logger.Error("自动解决告警失败",
				"alert_id", alert.ID,
				"service", serviceName,
				"rule", alert.RuleName,
				"error", err)
			continue
		}

		logger.Info("告警已自动解决",
			"alert_id", alert.ID,
			"service", serviceName,
			"rule", alert.RuleName,
			"duration", alert.Duration().String())

		// 根据配置决定是否发送告警恢复通知
		logger.Debug("检查是否发送恢复通知",
			"send_notification", ms.config.AutoResolve.SendNotification,
			"notification_service_exists", ms.notificationService != nil,
			"alert_id", alert.ID)
		if ms.config.AutoResolve.SendNotification && ms.notificationService != nil {
			go ms.sendAlertRecoveryNotification(&alert)
		}
	}
}

// sendAlertRecoveryNotification 发送告警恢复通知
func (ms *MonitorService) sendAlertRecoveryNotification(alert *model.Alert) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error("告警恢复通知发送时发生panic",
				"alert_id", alert.ID,
				"panic", r)
		}
	}()

	// 生成恢复通知内容
	recoveryContent := fmt.Sprintf("✅ 告警已自动恢复\n\n"+
		"服务名称: %s\n"+
		"告警规则: %s\n"+
		"严重程度: %s\n"+
		"原始消息: %s\n"+
		"告警开始时间: %s\n"+
		"告警持续时间: %s\n"+
		"恢复时间: %s",
		alert.ServiceName,
		alert.RuleName,
		string(alert.Severity),
		alert.Message,
		alert.StartedAt.Format("2006-01-02 15:04:05"),
		alert.Duration().String(),
		alert.ResolvedAt.Format("2006-01-02 15:04:05"))

	// 直接使用原告警ID发送恢复通知，避免创建新的告警对象
	// 查找匹配的通知分组
	notificationGroups := ms.notificationService.FindMatchingNotificationGroups(alert.ServiceName)
	if len(notificationGroups) == 0 {
		logger.Warn("未找到服务的通知分组", "service", alert.ServiceName)
		return
	}

	// 处理每个通知分组
	for _, group := range notificationGroups {
		if err := ms.sendRecoveryNotificationToGroup(alert.ID, group, recoveryContent); err != nil {
			logger.Error("发送恢复通知到分组失败",
				"group", group.Name,
				"alert_id", alert.ID,
				"error", err)
		}
	}

	logger.Info("告警恢复通知发送完成",
		"alert_id", alert.ID,
		"service", alert.ServiceName)
}

// sendRecoveryNotificationToGroup 发送恢复通知到指定分组
func (ms *MonitorService) sendRecoveryNotificationToGroup(alertID uint, group config.NotificationGroup, content string) error {
	logger.Info("发送恢复通知到分组",
		"group", group.Name,
		"alert_id", alertID,
		"channels", group.Channels)

	// 处理每个通知渠道
	for _, channelStr := range group.Channels {
		channel := model.NotificationChannel(channelStr)

		// 获取该渠道的接收者
		recipients, exists := group.Recipients[channelStr]
		if !exists || len(recipients) == 0 {
			logger.Warn("未找到渠道的接收者",
				"group", group.Name,
				"channel", channel)
			continue
		}

		// 为每个接收者创建通知
		for _, recipient := range recipients {
			req := &NotificationRequest{
				AlertID:   alertID,
				Channel:   channel,
				Recipient: recipient,
				Content:   content,
			}

			if err := ms.notificationService.SendNotification(req); err != nil {
				logger.Error("发送恢复通知失败",
					"group", group.Name,
					"channel", channel,
					"recipient", recipient,
					"error", err)
			}
		}
	}

	return nil
}

// autoResolveKafkaAlerts 自动解决Kafka告警
func (ms *MonitorService) autoResolveKafkaAlerts(serviceConfig config.ServiceConfig, consumerGroup string, groupMetrics *ConsumerGroupMetrics) {
	if !ms.config.AutoResolve.Enabled || ms.config.AlertRules == nil {
		return
	}

	for _, rule := range ms.config.AlertRules {
		// 检查规则是否适用于当前服务
		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		// 检查主题级别延迟告警是否需要自动解决
		if rule.Condition.Type == "topic_lag" {
			// 获取阈值配置
			var thresholdValue int64
			if threshold, ok := rule.Condition.Threshold.(int); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(float64); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(int64); ok {
				thresholdValue = threshold
			} else {
				continue // 阈值类型无效，跳过
			}

			if thresholdValue > 0 {
				// 计算恢复阈值（原阈值 * 恢复百分比）
				recoveryThreshold := int64(float64(thresholdValue) * float64(ms.config.AutoResolve.KafkaRecoveryThreshold) / 100.0)

				// 检查每个主题的延迟是否已恢复到阈值以下
				for topic, topicMetrics := range groupMetrics.TopicMetrics {
					if topicMetrics.TopicLag <= recoveryThreshold {
						// 延迟已恢复到安全水平，查找并解决相关告警
						alertKey := fmt.Sprintf("%s-%s-%s", rule.Name, consumerGroup, topic)
						ms.resolveKafkaAlert(alertKey, serviceConfig.Name, consumerGroup, topic, "topic_lag", topicMetrics.TopicLag, recoveryThreshold)
					}
				}
			}
		}

		// 检查分区级别延迟告警是否需要自动解决
		if rule.Condition.Type == "partition_lag" {
			// 获取阈值配置
			var thresholdValue int64
			if threshold, ok := rule.Condition.Threshold.(int); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(float64); ok {
				thresholdValue = int64(threshold)
			} else if threshold, ok := rule.Condition.Threshold.(int64); ok {
				thresholdValue = threshold
			} else {
				continue // 阈值类型无效，跳过
			}

			if thresholdValue > 0 {
				// 计算恢复阈值（原阈值 * 恢复百分比）
				recoveryThreshold := int64(float64(thresholdValue) * float64(ms.config.AutoResolve.KafkaRecoveryThreshold) / 100.0)

				// 检查所有分区的延迟是否已恢复到阈值以下
				for topic, topicMetrics := range groupMetrics.TopicMetrics {
					for partition, lag := range topicMetrics.PartitionLags {
						if lag <= recoveryThreshold {
							// 分区延迟已恢复到安全水平，查找并解决相关告警
							alertKey := fmt.Sprintf("%s-%s-%s-%d", rule.Name, consumerGroup, topic, partition)
							ms.resolveKafkaAlert(alertKey, serviceConfig.Name, consumerGroup, topic, "partition_lag", lag, recoveryThreshold)
						}
					}
				}
			}
		}
	}
}

// resolveKafkaAlert 解决特定的Kafka告警
func (ms *MonitorService) resolveKafkaAlert(alertKey, serviceName, consumerGroup, topic, alertType string, currentLag, recoveryThreshold int64) {
	// 查找活跃的告警
	existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceName)
	if err != nil {
		logger.Error("检查现有告警进行自动解决时失败",
			"alert_key", alertKey,
			"service", serviceName,
			"error", err)
		return
	}

	if existingAlert == nil {
		// 没有活跃告警，无需处理
		return
	}

	// 解决告警
	existingAlert.Resolve()
	if err := ms.alertRepo.Update(existingAlert); err != nil {
		logger.Error("自动解决Kafka告警失败",
			"alert_id", existingAlert.ID,
			"alert_key", alertKey,
			"service", serviceName,
			"error", err)
		return
	}

	logger.Info("Kafka告警已自动解决",
		"alert_id", existingAlert.ID,
		"service", serviceName,
		"consumer_group", consumerGroup,
		"topic", topic,
		"alert_type", alertType,
		"current_lag", currentLag,
		"recovery_threshold", recoveryThreshold,
		"duration", existingAlert.Duration().String())

	// 根据配置决定是否发送Kafka告警恢复通知
	if ms.config.AutoResolve.SendNotification && ms.notificationService != nil {
		go ms.sendKafkaAlertRecoveryNotification(existingAlert, consumerGroup, topic, alertType, currentLag, recoveryThreshold)
	}
}

// sendKafkaAlertRecoveryNotification 发送Kafka告警恢复通知
func (ms *MonitorService) sendKafkaAlertRecoveryNotification(alert *model.Alert, consumerGroup, topic, alertType string, currentLag, recoveryThreshold int64) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Kafka告警恢复通知发送时发生panic",
				"alert_id", alert.ID,
				"panic", r)
		}
	}()

	// 生成Kafka恢复通知内容
	recoveryContent := fmt.Sprintf("✅ Kafka告警已自动恢复\n\n"+
		"服务名称: %s\n"+
		"消费者组: %s\n"+
		"主题: %s\n"+
		"告警类型: %s\n"+
		"当前延迟: %d\n"+
		"恢复阈值: %d\n"+
		"严重程度: %s\n"+
		"原始消息: %s\n"+
		"告警开始时间: %s\n"+
		"告警持续时间: %s\n"+
		"恢复时间: %s",
		alert.ServiceName,
		consumerGroup,
		topic,
		alertType,
		currentLag,
		recoveryThreshold,
		string(alert.Severity),
		alert.Message,
		alert.StartedAt.Format("2006-01-02 15:04:05"),
		alert.Duration().String(),
		alert.ResolvedAt.Format("2006-01-02 15:04:05"))

	// 直接使用原告警ID发送恢复通知，避免创建新的告警对象
	// 查找匹配的通知分组
	notificationGroups := ms.notificationService.FindMatchingNotificationGroups(alert.ServiceName)
	if len(notificationGroups) == 0 {
		logger.Warn("未找到服务的通知分组", "service", alert.ServiceName)
		return
	}

	// 处理每个通知分组
	for _, group := range notificationGroups {
		if err := ms.sendRecoveryNotificationToGroup(alert.ID, group, recoveryContent); err != nil {
			logger.Error("发送Kafka恢复通知到分组失败",
				"group", group.Name,
				"alert_id", alert.ID,
				"error", err)
		}
	}

	logger.Info("Kafka告警恢复通知发送完成",
		"alert_id", alert.ID,
		"service", alert.ServiceName,
		"consumer_group", consumerGroup,
		"topic", topic)
}

// autoResolveEMQXAlerts 自动解决EMQX告警
func (ms *MonitorService) autoResolveEMQXAlerts(serviceConfig config.ServiceConfig, topicMetrics []*TopicRateMetrics) {
	if !ms.config.AutoResolve.Enabled || ms.config.AlertRules == nil {
		return
	}

	for _, rule := range ms.config.AlertRules {
		// 检查规则是否适用于当前服务
		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		// 检查EMQX速率下降告警是否需要自动解决
		if rule.Condition.Type == "emqx_rate_drop" {
			// 检查每个主题的速率是否已恢复
			for _, metric := range topicMetrics {
				if metric.BaselineRate > 0 {
					// 计算当前速率相对于基线的百分比
					currentRateRatio := metric.CurrentRate / metric.BaselineRate

					// 如果当前速率恢复到基线的指定百分比以上，则自动解决告警
					if currentRateRatio >= float64(ms.config.AutoResolve.EMQXRecoveryThreshold)/100.0 {
						// 构造唯一的告警标识，包含主题信息
						alertKey := fmt.Sprintf("%s-%s", rule.Name, metric.Topic)
						ms.resolveEMQXAlert(alertKey, serviceConfig.Name, metric.Topic, "emqx_rate_drop",
							metric.CurrentRate, metric.BaselineRate, currentRateRatio)
					}
				}
			}
		}
	}
}

// resolveEMQXAlert 解决特定的EMQX告警
func (ms *MonitorService) resolveEMQXAlert(alertKey, serviceName, topic, alertType string,
	currentRate, baselineRate, recoveryRatio float64) {
	// 查找活跃的告警
	existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceName)
	if err != nil {
		logger.Error("检查现有EMQX告警进行自动解决时失败",
			"alert_key", alertKey,
			"service", serviceName,
			"error", err)
		return
	}

	if existingAlert == nil {
		// 没有活跃告警，无需处理
		return
	}

	// 解决告警
	existingAlert.Resolve()
	if err := ms.alertRepo.Update(existingAlert); err != nil {
		logger.Error("自动解决EMQX告警失败",
			"alert_id", existingAlert.ID,
			"alert_key", alertKey,
			"service", serviceName,
			"error", err)
		return
	}

	logger.Info("EMQX告警已自动解决",
		"alert_id", existingAlert.ID,
		"service", serviceName,
		"topic", topic,
		"alert_type", alertType,
		"current_rate", fmt.Sprintf("%.2f", currentRate),
		"baseline_rate", fmt.Sprintf("%.2f", baselineRate),
		"recovery_ratio", fmt.Sprintf("%.2f%%", recoveryRatio*100),
		"recovery_threshold", fmt.Sprintf("%d%%", ms.config.AutoResolve.EMQXRecoveryThreshold),
		"duration", existingAlert.Duration().String())

	// 根据配置决定是否发送EMQX告警恢复通知
	if ms.config.AutoResolve.SendNotification && ms.notificationService != nil {
		go ms.sendEMQXAlertRecoveryNotification(existingAlert, topic, alertType, currentRate, baselineRate, recoveryRatio)
	}
}

// sendEMQXAlertRecoveryNotification 发送EMQX告警恢复通知
func (ms *MonitorService) sendEMQXAlertRecoveryNotification(alert *model.Alert, topic, alertType string,
	currentRate, baselineRate, recoveryRatio float64) {
	// 构造恢复通知内容
	recoveryContent := fmt.Sprintf("✅ EMQX告警已自动恢复\n\n"+
		"服务名称: %s\n"+
		"主题: %s\n"+
		"告警类型: %s\n"+
		"当前速率: %.2f msg/s\n"+
		"基线速率: %.2f msg/s\n"+
		"恢复比例: %.1f%%\n"+
		"严重程度: %s\n"+
		"原始消息: %s\n"+
		"告警开始时间: %s\n"+
		"告警持续时间: %s\n"+
		"恢复时间: %s",
		alert.ServiceName,
		topic,
		alertType,
		currentRate,
		baselineRate,
		recoveryRatio*100,
		string(alert.Severity),
		alert.Message,
		alert.StartedAt.Format("2006-01-02 15:04:05"),
		alert.Duration().String(),
		alert.ResolvedAt.Format("2006-01-02 15:04:05"))

	// 直接使用原告警ID发送恢复通知，避免创建新的告警对象
	// 查找匹配的通知分组
	notificationGroups := ms.notificationService.FindMatchingNotificationGroups(alert.ServiceName)
	if len(notificationGroups) == 0 {
		logger.Warn("未找到服务的通知分组", "service", alert.ServiceName)
		return
	}

	// 处理每个通知分组
	for _, group := range notificationGroups {
		if err := ms.sendRecoveryNotificationToGroup(alert.ID, group, recoveryContent); err != nil {
			logger.Error("发送EMQX恢复通知到分组失败",
				"group", group.Name,
				"alert_id", alert.ID,
				"error", err)
		}
	}

	logger.Info("EMQX告警恢复通知发送完成",
		"alert_id", alert.ID,
		"service", alert.ServiceName,
		"topic", topic)
}

// getOrCreateKafkaMonitorForInstance 获取或创建Kafka实例监控器
func (ms *MonitorService) getOrCreateKafkaMonitorForInstance(serviceConfig config.ServiceConfig) (*KafkaMonitor, error) {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	// 使用实例级别的key
	instanceKey := fmt.Sprintf("%s:%s", serviceConfig.Name, serviceConfig.InstanceID)

	// 检查是否已存在
	if monitor, exists := ms.kafkaMonitors[instanceKey]; exists {
		return monitor, nil
	}

	// 创建新的Kafka监控器
	kafkaMonitor, err := NewKafkaMonitor(serviceConfig.Kafka)
	if err != nil {
		return nil, fmt.Errorf("创建Kafka实例监控器失败: %w", err)
	}

	// 存储监控器
	ms.kafkaMonitors[instanceKey] = kafkaMonitor

	logger.Info("创建Kafka实例监控器成功",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID,
		"instance_key", instanceKey)

	return kafkaMonitor, nil
}

// getOrCreateEMQXMonitorForInstance 获取或创建EMQX实例监控器
func (ms *MonitorService) getOrCreateEMQXMonitorForInstance(serviceConfig config.ServiceConfig) (*EMQXMonitor, error) {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	// 使用实例级别的key
	instanceKey := fmt.Sprintf("%s:%s", serviceConfig.Name, serviceConfig.InstanceID)

	// 检查是否已存在
	if monitor, exists := ms.emqxMonitors[instanceKey]; exists {
		return monitor, nil
	}

	// 创建新的EMQX监控器（使用现有构造函数）
	emqxMonitor, err := NewEMQXMonitor(instanceKey, serviceConfig.EMQX, nil)
	if err != nil {
		return nil, fmt.Errorf("创建EMQX实例监控器失败: %w", err)
	}

	// 存储监控器
	ms.emqxMonitors[instanceKey] = emqxMonitor

	logger.Info("创建EMQX实例监控器成功",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID,
		"instance_key", instanceKey)

	return emqxMonitor, nil
}

// 注意：实例级别的指标存储方法已简化，避免使用不存在的类型
// 在实际实现中，可以根据需要扩展这些方法

// checkAlertRulesForInstance 检查实例级别的告警规则
func (ms *MonitorService) checkAlertRulesForInstance(serviceConfig config.ServiceConfig, serviceHealth *model.ServiceHealth) {
	for _, rule := range ms.config.AlertRules {
		if !rule.Enabled {
			continue
		}

		// 检查服务是否在规则范围内
		serviceInRule := false
		for _, serviceName := range rule.Services {
			if serviceName == serviceConfig.Name {
				serviceInRule = true
				break
			}
		}

		if !serviceInRule {
			continue
		}

		// 检查是否应该触发告警
		if ms.shouldTriggerAlert(rule, serviceHealth) {
			ms.createInstanceAlert(rule, serviceConfig, serviceHealth)
		}
	}
}

// createInstanceAlert 创建实例级别告警
func (ms *MonitorService) createInstanceAlert(rule config.AlertRuleConfig, serviceConfig config.ServiceConfig, serviceHealth *model.ServiceHealth) {
	// 检查是否已存在相同的活跃告警（包含实例ID）
	alertKey := fmt.Sprintf("%s:%s", rule.Name, serviceConfig.InstanceID)
	existingAlert, err := ms.alertRepo.FindExistingAlert(alertKey, serviceConfig.Name)
	if err != nil {
		logger.Error("检查现有实例告警失败", "error", err)
		return
	}

	if existingAlert != nil {
		logger.Debug("实例告警已存在，跳过创建",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID,
			"rule", rule.Name)
		return
	}

	// 创建告警记录
	alert := &model.Alert{
		RuleName:    alertKey, // 使用包含实例ID的key
		ServiceName: serviceConfig.Name,
		Severity:    model.AlertSeverityCritical, // 使用正确的类型
		Message:     fmt.Sprintf("服务实例 %s (实例ID: %s) 触发告警规则: %s", serviceConfig.Name, serviceConfig.InstanceID, rule.Name),
		Status:      model.AlertStatusFiring, // 使用正确的状态
		StartedAt:   time.Now(),
		Metadata:    fmt.Sprintf(`{"instance_id":"%s","consecutive_failures":%d}`, serviceConfig.InstanceID, serviceHealth.ConsecutiveFailures),
	}

	if err := ms.alertRepo.Create(alert); err != nil {
		logger.Error("创建实例告警失败",
			"service", serviceConfig.Name,
			"instance_id", serviceConfig.InstanceID,
			"rule", rule.Name,
			"error", err)
		return
	}

	logger.Info("创建实例告警成功",
		"service", serviceConfig.Name,
		"instance_id", serviceConfig.InstanceID,
		"rule", rule.Name,
		"alert_id", alert.ID)

	// 发送告警通知
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error("发送实例告警通知时发生panic",
					"service", serviceConfig.Name,
					"instance_id", serviceConfig.InstanceID,
					"panic", r)
			}
		}()

		if err := ms.notificationService.SendAlertNotifications(alert); err != nil {
			logger.Error("发送实例告警通知失败",
				"service", serviceConfig.Name,
				"instance_id", serviceConfig.InstanceID,
				"alert_id", alert.ID,
				"error", err)
		}
	}()
}

// autoResolveServiceInstanceAlerts 自动解决服务实例的活跃告警
func (ms *MonitorService) autoResolveServiceInstanceAlerts(serviceName, instanceID string) {
	// 获取该服务实例的所有活跃告警
	activeAlerts, err := ms.alertRepo.GetActiveAlertsByService(serviceName)
	if err != nil {
		logger.Error("获取活跃实例告警进行自动解决时失败",
			"service", serviceName,
			"instance_id", instanceID,
			"error", err)
		return
	}

	for _, alert := range activeAlerts {
		// 检查告警是否属于这个实例
		if !strings.Contains(alert.RuleName, instanceID) {
			continue
		}

		// 解决告警
		alert.Status = model.AlertStatusResolved
		now := time.Now()
		alert.ResolvedAt = &now

		if err := ms.alertRepo.Update(&alert); err != nil {
			logger.Error("自动解决实例告警失败",
				"service", serviceName,
				"instance_id", instanceID,
				"alert_id", alert.ID,
				"error", err)
			continue
		}

		logger.Info("自动解决实例告警成功",
			"service", serviceName,
			"instance_id", instanceID,
			"alert_id", alert.ID,
			"rule", alert.RuleName)

		// 根据配置决定是否发送恢复通知
		logger.Debug("检查是否发送实例恢复通知",
			"send_notification", ms.config.AutoResolve.SendNotification,
			"notification_service_exists", ms.notificationService != nil,
			"alert_id", alert.ID)
		if ms.config.AutoResolve.SendNotification && ms.notificationService != nil {
			go ms.sendInstanceAlertRecoveryNotification(&alert, instanceID)
		}
	}
}

// sendInstanceAlertRecoveryNotification 发送实例告警恢复通知
func (ms *MonitorService) sendInstanceAlertRecoveryNotification(alert *model.Alert, instanceID string) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error("实例告警恢复通知发送时发生panic",
				"alert_id", alert.ID,
				"instance_id", instanceID,
				"panic", r)
		}
	}()

	// 构造恢复通知内容
	recoveryContent := fmt.Sprintf("✅ 服务实例告警已自动恢复\n\n"+
		"服务名称: %s\n"+
		"实例ID: %s\n"+
		"告警规则: %s\n"+
		"严重程度: %s\n"+
		"原始消息: %s\n"+
		"告警开始时间: %s\n"+
		"告警持续时间: %s\n"+
		"恢复时间: %s",
		alert.ServiceName,
		instanceID,
		alert.RuleName,
		string(alert.Severity),
		alert.Message,
		alert.StartedAt.Format("2006-01-02 15:04:05"),
		alert.Duration().String(),
		alert.ResolvedAt.Format("2006-01-02 15:04:05"))

	// 直接使用原告警ID发送恢复通知，避免创建新的告警对象
	// 查找匹配的通知分组
	notificationGroups := ms.notificationService.FindMatchingNotificationGroups(alert.ServiceName)
	if len(notificationGroups) == 0 {
		logger.Warn("未找到服务的通知分组", "service", alert.ServiceName)
		return
	}

	// 处理每个通知分组
	for _, group := range notificationGroups {
		if err := ms.sendRecoveryNotificationToGroup(alert.ID, group, recoveryContent); err != nil {
			logger.Error("发送实例恢复通知到分组失败",
				"group", group.Name,
				"alert_id", alert.ID,
				"error", err)
		}
	}

	logger.Info("实例告警恢复通知发送完成",
		"alert_id", alert.ID,
		"instance_id", instanceID,
		"service", alert.ServiceName)
}

// autoResolveEMQXInstanceAlerts 自动解决EMQX实例告警
func (ms *MonitorService) autoResolveEMQXInstanceAlerts(serviceConfig config.ServiceConfig, topicMetrics []*TopicRateMetrics) {
	if !ms.config.AutoResolve.Enabled || ms.config.AlertRules == nil {
		return
	}

	for _, rule := range ms.config.AlertRules {
		if !ms.isRuleApplicableToService(rule, serviceConfig.Name) {
			continue
		}

		if rule.Condition.Type == "emqx_rate_drop" {
			for _, topicMetric := range topicMetrics {
				alertKey := fmt.Sprintf("%s-%s:%s", rule.Name, topicMetric.Topic, serviceConfig.InstanceID)

				// 这里可以添加具体的恢复条件检查逻辑
				// 暂时跳过具体实现，保持与原有逻辑一致
				logger.Debug("检查EMQX实例告警恢复条件",
					"service", serviceConfig.Name,
					"instance_id", serviceConfig.InstanceID,
					"topic", topicMetric.Topic,
					"alert_key", alertKey)
			}
		}
	}
}
