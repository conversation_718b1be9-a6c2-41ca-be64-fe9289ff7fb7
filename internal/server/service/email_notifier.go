package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/shared/logger"
)

// EmailNotifier 邮件通知器
type EmailNotifier struct {
	config     *config.Config
	httpClient *http.Client
}

// EmailSendRequest 邮件发送请求
type EmailSendRequest struct {
	To      string `json:"to"`      // 收件人邮箱
	Subject string `json:"subject"` // 邮件主题
	Content string `json:"content"` // 邮件内容
}

// EmailResponse 邮件响应（JSON格式）
type EmailResponse struct {
	Code    int         `json:"code"`    // 响应状态码
	Success bool        `json:"success"` // 是否成功
	Data    interface{} `json:"data"`    // 响应数据
	Message string      `json:"msg"`     // 响应消息
}

// NewEmailNotifier 创建邮件通知器
func NewEmailNotifier(cfg *config.Config) *EmailNotifier {
	// 设置超时时间
	timeout := 30 * time.Second
	if cfg.Notifications.Channels.Email != nil && cfg.Notifications.Channels.Email.TimeoutSeconds > 0 {
		timeout = time.Duration(cfg.Notifications.Channels.Email.TimeoutSeconds) * time.Second
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: timeout,
	}

	return &EmailNotifier{
		config:     cfg,
		httpClient: httpClient,
	}
}

// SendEmail 发送邮件
func (en *EmailNotifier) SendEmail(req *EmailSendRequest) (*EmailResponse, error) {
	logger.Info("发送邮件通知",
		"to", req.To,
		"subject", req.Subject,
		"content_length", len(req.Content))

	// 获取邮件配置
	emailConfig := en.config.Notifications.Channels.Email
	if emailConfig == nil || emailConfig.URL == "" {
		return nil, fmt.Errorf("邮件配置未找到或URL未设置")
	}

	// 执行HTTP请求
	response, err := en.executeRequest(emailConfig.URL, req)
	if err != nil {
		return nil, fmt.Errorf("发送邮件失败: %w", err)
	}

	logger.Info("邮件发送成功",
		"response_code", response.Code,
		"success", response.Success,
		"message", response.Message,
		"url", emailConfig.URL)

	return response, nil
}

// executeRequest 执行HTTP POST请求
func (en *EmailNotifier) executeRequest(requestURL string, req *EmailSendRequest) (*EmailResponse, error) {
	// 准备表单数据
	formData := url.Values{
		"to":      {req.To},
		"subject": {req.Subject},
		"content": {req.Content},
	}

	// 创建POST请求
	httpReq, err := http.NewRequest("POST", requestURL, bytes.NewBufferString(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 执行请求
	resp, err := en.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求执行失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP错误: 状态码 %d", resp.StatusCode)
	}

	// 解析JSON响应
	var emailResponse EmailResponse
	if err := json.NewDecoder(resp.Body).Decode(&emailResponse); err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	return &emailResponse, nil
}

// IsSuccessResponse 判断是否为成功响应
func (en *EmailNotifier) IsSuccessResponse(response *EmailResponse) bool {
	return response != nil && response.Success && response.Code == 200
}

// GetErrorMessage 获取错误信息
func (en *EmailNotifier) GetErrorMessage(response *EmailResponse) string {
	if response == nil {
		return "响应为空"
	}

	if !response.Success {
		if response.Message != "" {
			return response.Message
		}
		return fmt.Sprintf("邮件发送失败，错误码: %d", response.Code)
	}

	return ""
}
