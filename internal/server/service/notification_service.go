package service

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/model"
	"monitor/internal/server/repository"
	"monitor/internal/shared/logger"
)

// NotificationService 通知服务
type NotificationService struct {
	config           *config.Config
	notificationRepo *repository.NotificationRepository
	smsNotifier      *SMSNotifier
	emailNotifier    *EmailNotifier
	mu               sync.RWMutex
	stopChan         chan struct{}
	retryTicker      *time.Ticker
}

// NotificationRequest 通知请求
type NotificationRequest struct {
	AlertID   uint
	Channel   model.NotificationChannel
	Recipient string
	Content   string
	Metadata  map[string]interface{}
}

// NewNotificationService 创建通知服务实例
func NewNotificationService(cfg *config.Config) *NotificationService {
	ns := &NotificationService{
		config:           cfg,
		notificationRepo: repository.NewNotificationRepository(),
		smsNotifier:      NewSMSNotifier(cfg),
		emailNotifier:    NewEmailNotifier(cfg),
		stopChan:         make(chan struct{}),
	}

	// 启动重试机制
	ns.startRetryWorker()

	return ns
}

// SendNotification 发送单个通知
func (ns *NotificationService) SendNotification(req *NotificationRequest) error {
	logger.Info("发送通知",
		"alert_id", req.AlertID,
		"channel", req.Channel,
		"recipient", req.Recipient)

	// 创建通知记录
	notification := &model.Notification{
		AlertID:   req.AlertID,
		Channel:   req.Channel,
		Recipient: req.Recipient,
		Content:   req.Content,
		Status:    model.NotificationStatusPending,
	}

	// 保存到数据库
	if err := ns.notificationRepo.Create(notification); err != nil {
		return fmt.Errorf("创建通知记录失败: %w", err)
	}

	// 异步发送通知
	go ns.sendNotificationAsync(notification, req.Content)

	return nil
}

// SendAlertNotifications 根据告警和通知分组发送通知
func (ns *NotificationService) SendAlertNotifications(alert *model.Alert) error {
	logger.Info("处理告警通知",
		"alert_id", alert.ID,
		"service", alert.ServiceName,
		"severity", alert.Severity)

	// 查找匹配的通知分组
	notificationGroups := ns.findMatchingNotificationGroups(alert.ServiceName)
	if len(notificationGroups) == 0 {
		logger.Warn("未找到服务的通知分组", "service", alert.ServiceName)
		return nil
	}

	// 生成告警内容
	content := ns.generateAlertContent(alert)

	// 处理每个通知分组
	for _, group := range notificationGroups {
		if err := ns.processNotificationGroup(alert, group, content); err != nil {
			logger.Error("处理通知组失败",
				"group", group.Name,
				"alert_id", alert.ID,
				"error", err)
		}
	}

	return nil
}

// processNotificationGroup 处理通知分组逻辑
func (ns *NotificationService) processNotificationGroup(alert *model.Alert, group config.NotificationGroup, content string) error {
	logger.Info("处理通知组",
		"group", group.Name,
		"alert_id", alert.ID,
		"channels", group.Channels)

	// 处理每个通知渠道
	for _, channelStr := range group.Channels {
		channel := model.NotificationChannel(channelStr)

		// 获取该渠道的接收者
		recipients, exists := group.Recipients[channelStr]
		if !exists || len(recipients) == 0 {
			logger.Warn("未找到渠道的接收者",
				"group", group.Name,
				"channel", channel)
			continue
		}

		// 为每个接收者创建通知
		for _, recipient := range recipients {
			req := &NotificationRequest{
				AlertID:   alert.ID,
				Channel:   channel,
				Recipient: recipient,
				Content:   content,
			}

			if err := ns.SendNotification(req); err != nil {
				logger.Error("发送通知失败",
					"group", group.Name,
					"channel", channel,
					"recipient", recipient,
					"error", err)
			}
		}
	}

	return nil
}

// sendNotificationAsync 异步发送通知
func (ns *NotificationService) sendNotificationAsync(notification *model.Notification, content string) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error("异步通知发送时发生panic",
				"notification_id", notification.ID,
				"panic", r)
		}
	}()

	var err error
	switch notification.Channel {
	case model.NotificationChannelSMS:
		err = ns.sendSMSNotification(notification, content)
	case model.NotificationChannelEmail:
		err = ns.sendEmailNotification(notification, content)
	case model.NotificationChannelWebhook:
		err = ns.sendWebhookNotification(notification, content)
	default:
		err = fmt.Errorf("不支持的通知渠道: %s", notification.Channel)
	}

	// 更新通知状态
	if err != nil {
		logger.Error("发送通知失败",
			"notification_id", notification.ID,
			"channel", notification.Channel,
			"recipient", notification.Recipient,
			"error", err)

		if updateErr := ns.notificationRepo.MarkAsFailed(notification.ID, err.Error()); updateErr != nil {
			logger.Error("标记通知为失败状态失败",
				"notification_id", notification.ID,
				"error", updateErr)
		}
	} else {
		logger.Info("通知发送成功",
			"notification_id", notification.ID,
			"channel", notification.Channel,
			"recipient", notification.Recipient)

		if updateErr := ns.notificationRepo.MarkAsSent(notification.ID); updateErr != nil {
			logger.Error("标记通知为已发送状态失败",
				"notification_id", notification.ID,
				"error", updateErr)
		}
	}
}

// sendSMSNotification 发送短信通知
func (ns *NotificationService) sendSMSNotification(notification *model.Notification, content string) error {
	// 获取短信配置
	smsConfig := ns.config.Notifications.Channels.SMS
	if smsConfig == nil || smsConfig.Username == "" {
		return fmt.Errorf("未找到短信配置")
	}

	// 构建短信发送请求
	req := &SMSSendRequest{
		Account:  smsConfig.Username,
		Password: smsConfig.Password,
		Phone:    notification.Recipient,
		Content:  content,
		SendTime: "", // 立即发送
	}

	// 发送短信
	response, err := ns.smsNotifier.SendSMS(req)
	if err != nil {
		return fmt.Errorf("发送短信失败: %w", err)
	}

	// 检查响应
	if !ns.smsNotifier.IsSuccessResponse(response) {
		errorMsg := ns.smsNotifier.GetErrorMessage(response)
		return fmt.Errorf("短信发送失败: %s", errorMsg)
	}

	return nil
}

// sendEmailNotification 发送邮件通知
func (ns *NotificationService) sendEmailNotification(notification *model.Notification, content string) error {
	// 获取邮件配置
	emailConfig := ns.config.Notifications.Channels.Email
	if emailConfig == nil || emailConfig.URL == "" {
		return fmt.Errorf("未找到邮件配置或URL未设置")
	}

	// 构建邮件发送请求
	req := &EmailSendRequest{
		To:      notification.Recipient,
		Subject: "系统监控告警通知", // 可以从配置或通知内容中提取
		Content: content,
	}

	// 发送邮件
	response, err := ns.emailNotifier.SendEmail(req)
	if err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	// 检查响应
	if !ns.emailNotifier.IsSuccessResponse(response) {
		errorMsg := ns.emailNotifier.GetErrorMessage(response)
		return fmt.Errorf("邮件发送失败: %s", errorMsg)
	}

	return nil
}

// sendWebhookNotification 发送Webhook通知（占位符实现）
func (ns *NotificationService) sendWebhookNotification(notification *model.Notification, content string) error {
	// TODO: 实现Webhook发送逻辑
	logger.Info("Webhook通知尚未实现",
		"notification_id", notification.ID,
		"recipient", notification.Recipient)
	return fmt.Errorf("Webhook通知尚未实现")
}

// FindMatchingNotificationGroups 查找匹配的通知分组（公开方法）
func (ns *NotificationService) FindMatchingNotificationGroups(serviceName string) []config.NotificationGroup {
	return ns.findMatchingNotificationGroups(serviceName)
}

// findMatchingNotificationGroups 查找匹配的通知分组
func (ns *NotificationService) findMatchingNotificationGroups(serviceName string) []config.NotificationGroup {
	var matchingGroups []config.NotificationGroup

	for _, group := range ns.config.Notifications.Groups {
		// 检查服务名是否匹配
		for _, service := range group.Services {
			if service == serviceName || service == "*" {
				matchingGroups = append(matchingGroups, group)
				break
			}
		}
	}

	return matchingGroups
}

// generateAlertContent 生成告警内容
func (ns *NotificationService) generateAlertContent(alert *model.Alert) string {
	var content strings.Builder

	content.WriteString(fmt.Sprintf("【监控告警】\n"))
	content.WriteString(fmt.Sprintf("服务：%s\n", alert.ServiceName))
	content.WriteString(fmt.Sprintf("级别：%s\n", alert.Severity))
	content.WriteString(fmt.Sprintf("状态：%s\n", alert.Status))
	content.WriteString(fmt.Sprintf("消息：%s\n", alert.Message))
	content.WriteString(fmt.Sprintf("时间：%s", alert.StartedAt.Format("2006-01-02 15:04:05")))

	return content.String()
}

// startRetryWorker 启动重试工作器
func (ns *NotificationService) startRetryWorker() {
	ns.retryTicker = time.NewTicker(5 * time.Minute) // 每5分钟检查一次

	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error("重试工作器发生panic", "panic", r)
			}
		}()

		for {
			select {
			case <-ns.retryTicker.C:
				ns.retryFailedNotifications()
			case <-ns.stopChan:
				return
			}
		}
	}()

	logger.Info("通知重试工作器已启动")
}

// retryFailedNotifications 重试失败的通知
func (ns *NotificationService) retryFailedNotifications() {
	maxRetries := 3
	failedNotifications, err := ns.notificationRepo.GetFailedNotifications(maxRetries)
	if err != nil {
		logger.Error("获取失败通知失败", "error", err)
		return
	}

	if len(failedNotifications) == 0 {
		return
	}

	logger.Info("重试失败的通知", "count", len(failedNotifications))

	for _, notification := range failedNotifications {
		// 重新生成内容
		alert, err := ns.getAlertByID(notification.AlertID)
		if err != nil {
			logger.Error("获取告警信息进行重试失败",
				"notification_id", notification.ID,
				"alert_id", notification.AlertID,
				"error", err)
			continue
		}

		content := ns.generateAlertContent(alert)

		// 异步重试
		go ns.sendNotificationAsync(&notification, content)
	}
}

// getAlertByID 根据ID获取告警（辅助方法）
func (ns *NotificationService) getAlertByID(alertID uint) (*model.Alert, error) {
	alertRepo := repository.NewAlertRepository()
	return alertRepo.GetByID(alertID)
}

// Stop 停止通知服务
func (ns *NotificationService) Stop() {
	ns.mu.Lock()
	defer ns.mu.Unlock()

	if ns.retryTicker != nil {
		ns.retryTicker.Stop()
	}

	close(ns.stopChan)
	logger.Info("通知服务已停止")
}

// GetNotificationStats 获取通知统计信息
func (ns *NotificationService) GetNotificationStats() (map[string]int64, error) {
	return ns.notificationRepo.GetNotificationStats()
}
