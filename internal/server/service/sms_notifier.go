package service

import (
	"encoding/xml"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/shared/logger"
)

// SMSNotifier 短信通知器
type SMSNotifier struct {
	config     *config.Config
	httpClient *http.Client
}

// SMSSendRequest 短信发送请求
type SMSSendRequest struct {
	Account  string
	Password string
	Phone    string
	Content  string
	SendTime string
}

// SMSResponse 短信响应（XML格式）
type SMSResponse struct {
	XMLName  xml.Name `xml:"result"`
	Response int      `xml:"response"`
	SMS      *SMSInfo `xml:"sms,omitempty"`
}

// SMSInfo 短信信息
type SMSInfo struct {
	Phone string `xml:"phone"`
	SMSID string `xml:"smsID"`
	Stat  string `xml:"stat,omitempty"`
}

// NewSMSNotifier 创建短信通知器
func NewSMSNotifier(cfg *config.Config) *SMSNotifier {
	// 设置超时时间
	timeout := 30 * time.Second
	if cfg.Notifications.Channels.SMS != nil && cfg.Notifications.Channels.SMS.TimeoutSeconds > 0 {
		timeout = time.Duration(cfg.Notifications.Channels.SMS.TimeoutSeconds) * time.Second
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: timeout,
	}

	return &SMSNotifier{
		config:     cfg,
		httpClient: httpClient,
	}
}

// SendSMS 发送短信
func (sn *SMSNotifier) SendSMS(req *SMSSendRequest) (*SMSResponse, error) {
	logger.Info("发送短信通知",
		"account", req.Account,
		"phone", req.Phone,
		"content_length", len(req.Content))

	// 获取短信配置
	smsConfig := sn.config.Notifications.Channels.SMS
	if smsConfig == nil || smsConfig.URL == "" {
		return nil, fmt.Errorf("短信配置未找到或URL未设置")
	}

	// 准备表单数据
	formData := url.Values{
		"Account":  {req.Account},
		"Password": {req.Password},
		"Phone":    {req.Phone},
		"Content":  {req.Content},
		"SendTime": {req.SendTime},
	}

	// 执行HTTP请求
	response, err := sn.executeRequest(smsConfig.URL, formData)
	if err != nil {
		return nil, fmt.Errorf("发送短信失败: %w", err)
	}

	logger.Info("短信发送成功",
		"response_code", response.Response,
		"sms_id", getSMSID(response),
		"url", smsConfig.URL)

	return response, nil
}

// executeRequest 执行HTTP POST请求
func (sn *SMSNotifier) executeRequest(requestURL string, formData url.Values) (*SMSResponse, error) {
	// 创建POST请求
	req, err := http.NewRequest("POST", requestURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 执行请求
	resp, err := sn.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求执行失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP错误: 状态码 %d", resp.StatusCode)
	}

	// 解析XML响应
	var smsResponse SMSResponse
	if err := xml.NewDecoder(resp.Body).Decode(&smsResponse); err != nil {
		return nil, fmt.Errorf("解析XML响应失败: %w", err)
	}

	return &smsResponse, nil
}

// getSMSID 从响应中获取短信ID
func getSMSID(response *SMSResponse) string {
	if response != nil && response.SMS != nil {
		return response.SMS.SMSID
	}
	return ""
}

// getSMSStatus 从响应中获取短信状态
func getSMSStatus(response *SMSResponse) string {
	if response != nil && response.SMS != nil {
		return response.SMS.Stat
	}
	return ""
}

// IsSuccessResponse 判断是否为成功响应
func (sn *SMSNotifier) IsSuccessResponse(response *SMSResponse) bool {
	return response != nil && response.Response > 0
}

// GetErrorMessage 获取错误信息
func (sn *SMSNotifier) GetErrorMessage(response *SMSResponse) string {
	if response == nil {
		return "响应为空"
	}

	switch response.Response {
	case -1:
		return "账号不存在，请检查用户名或密码是否正确"
	case -2:
		return "账户余额不足"
	case -3:
		return "账号已经被禁用"
	case -4:
		return "IP鉴权失败"
	case -8:
		return "缺少请求参数或参数名称不正确"
	case -9:
		return "内容不合法"
	case -10:
		return "账户当日发送量已经超过允许发送的数量"
	case -11:
		return "通道未配置"
	case -12:
		return "超过单次发送上限"
	case -13:
		return "流速超速"
	case -14:
		return "服务内部错误"
	default:
		if response.Response > 0 {
			return ""
		}
		return fmt.Sprintf("未知错误码: %d", response.Response)
	}
}
