package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/shared/logger"
)

// RemoteDockerService 远程Docker服务
type RemoteDockerService struct {
	httpClient *http.Client
}

// NewRemoteDockerService 创建远程Docker服务
func NewRemoteDockerService() *RemoteDockerService {
	return &RemoteDockerService{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// RestartContainer 通过Agent重启容器
func (r *RemoteDockerService) RestartContainer(serviceConfig config.ServiceConfig) error {
	if serviceConfig.Docker == nil || serviceConfig.Docker.Agent == nil {
		return fmt.Errorf("agent config not found for service %s", serviceConfig.Name)
	}

	agent := serviceConfig.Docker.Agent
	containerName := serviceConfig.Docker.ContainerName

	url := fmt.Sprintf("http://%s:%d/api/v1/docker/containers/%s/restart",
		agent.Host, agent.Port, containerName)

	logger.Info("尝试通过代理重启容器",
		"service", serviceConfig.Name,
		"container", containerName,
		"agent", fmt.Sprintf("%s:%d", agent.Host, agent.Port))

	// 创建请求
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置认证头
	req.Header.Set("Authorization", "Bearer "+agent.Token)
	req.Header.Set("Content-Type", "application/json")

	// 执行请求（支持重试）
	var lastErr error
	maxRetries := agent.RetryCount
	if maxRetries == 0 {
		maxRetries = 3
	}

	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err := r.httpClient.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("request failed (attempt %d): %w", attempt+1, err)
			if attempt < maxRetries {
				logger.Debug("代理请求失败，正在重试",
					"service", serviceConfig.Name,
					"attempt", attempt+1,
					"error", err)
				time.Sleep(time.Second * time.Duration(attempt+1)) // 递增延迟
				continue
			}
			break
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			logger.Info("通过代理成功重启容器",
				"service", serviceConfig.Name,
				"container", containerName,
				"agent", fmt.Sprintf("%s:%d", agent.Host, agent.Port))
			return nil
		}

		// 读取错误响应
		var errorResp map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&errorResp); err == nil {
			lastErr = fmt.Errorf("agent returned error (status %d): %v", resp.StatusCode, errorResp["error"])
		} else {
			lastErr = fmt.Errorf("agent returned error status: %d", resp.StatusCode)
		}

		if attempt < maxRetries {
			logger.Debug("代理请求失败，正在重试",
				"service", serviceConfig.Name,
				"attempt", attempt+1,
				"status", resp.StatusCode)
			time.Sleep(time.Second * time.Duration(attempt+1))
		}
	}

	return fmt.Errorf("failed to restart container after %d attempts: %w", maxRetries+1, lastErr)
}

// GetContainerStatus 通过Agent获取容器状态
func (r *RemoteDockerService) GetContainerStatus(serviceConfig config.ServiceConfig) (*AgentContainerStatus, error) {
	if serviceConfig.Docker == nil || serviceConfig.Docker.Agent == nil {
		return nil, fmt.Errorf("agent config not found for service %s", serviceConfig.Name)
	}

	agent := serviceConfig.Docker.Agent
	containerName := serviceConfig.Docker.ContainerName

	url := fmt.Sprintf("http://%s:%d/api/v1/docker/containers/%s/status",
		agent.Host, agent.Port, containerName)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+agent.Token)

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("agent returned error status: %d", resp.StatusCode)
	}

	var response struct {
		Data AgentContainerStatus `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response.Data, nil
}

// ExecuteHealthCheck 通过Agent执行健康检查
func (r *RemoteDockerService) ExecuteHealthCheck(serviceConfig config.ServiceConfig) (*AgentHealthCheckResult, error) {
	if serviceConfig.Docker == nil || serviceConfig.Docker.Agent == nil {
		return nil, fmt.Errorf("agent config not found for service %s", serviceConfig.Name)
	}

	agent := serviceConfig.Docker.Agent
	url := fmt.Sprintf("http://%s:%d/api/v1/health/check", agent.Host, agent.Port)

	// 构建健康检查请求
	checkReq := map[string]interface{}{
		"endpoint": serviceConfig.Endpoint,
		"method":   serviceConfig.Method,
		"timeout":  serviceConfig.Timeout,
	}

	jsonData, err := json.Marshal(checkReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+agent.Token)
	req.Header.Set("Content-Type", "application/json")

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	var response struct {
		Data AgentHealthCheckResult `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response.Data, nil
}

// AgentContainerStatus Agent返回的容器状态
type AgentContainerStatus struct {
	ID      string    `json:"id"`
	Name    string    `json:"name"`
	Status  string    `json:"status"`
	State   string    `json:"state"`
	Created time.Time `json:"created"`
	Image   string    `json:"image"`
}

// AgentHealthCheckResult Agent返回的健康检查结果
type AgentHealthCheckResult struct {
	Endpoint     string `json:"endpoint"`
	Method       string `json:"method"`
	Success      bool   `json:"success"`
	StatusCode   int    `json:"status_code"`
	ResponseTime int64  `json:"response_time"`
	Timestamp    string `json:"timestamp"`
	Error        string `json:"error,omitempty"`
}

// IsHealthy 判断容器是否健康
func (s *AgentContainerStatus) IsHealthy() bool {
	return s.State == "running"
}
