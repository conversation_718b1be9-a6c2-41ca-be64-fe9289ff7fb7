package service

import (
	"crypto/tls"
	"fmt"
	"sync"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/shared/logger"

	"github.com/IBM/sarama"
)

// KafkaMonitor Kafka监控器
type KafkaMonitor struct {
	config      *config.KafkaConfig
	client      sarama.Client
	admin       sarama.ClusterAdmin
	mu          sync.RWMutex
	isConnected bool
	lastError   error
	retryCount  int
	maxRetries  int
	timeout     time.Duration
}

// TopicLagMetrics 单个主题的延迟指标
type TopicLagMetrics struct {
	Topic           string          // 主题名称
	ConsumerGroup   string          // 消费者组名称
	TopicLag        int64           // 消费者组消费该主题的总延迟
	PartitionLags   map[int32]int64 // 分区延迟 [partition]lag
	ConsumerOffsets map[int32]int64 // 消费者偏移量 [partition]offset
	ProducerOffsets map[int32]int64 // 生产者偏移量 [partition]offset
	PartitionCount  int             // 该主题的分区数
	MaxLagPartition int32           // 最大延迟分区
	ConnectivityOK  bool            // 连接状态
}

// ConsumerGroupMetrics 消费者组的完整指标（包含所有主题）
type ConsumerGroupMetrics struct {
	ConsumerGroup  string                      // 消费者组名称
	TopicMetrics   map[string]*TopicLagMetrics // 各主题的延迟指标 [topic]metrics
	TotalLag       int64                       // 消费者组所有主题的总延迟
	TopicCount     int                         // 主题总数
	ConnectivityOK bool                        // 连接状态
}

// NewKafkaMonitor 创建Kafka监控实例
func NewKafkaMonitor(kafkaConfig *config.KafkaConfig) (*KafkaMonitor, error) {
	if kafkaConfig == nil {
		return nil, fmt.Errorf("kafka config cannot be nil")
	}

	monitor := &KafkaMonitor{
		config:     kafkaConfig,
		maxRetries: kafkaConfig.RetryCount,
		timeout:    time.Duration(kafkaConfig.Timeout) * time.Second,
	}

	// 初始化连接
	if err := monitor.connect(); err != nil {
		return nil, fmt.Errorf("failed to initialize kafka connection: %w", err)
	}

	return monitor, nil
}

// connect 建立Kafka连接
func (km *KafkaMonitor) connect() error {
	km.mu.Lock()
	defer km.mu.Unlock()

	// 创建Sarama配置
	saramaConfig := sarama.NewConfig()

	// 设置Kafka版本
	if km.config.Version != "" {
		version, err := sarama.ParseKafkaVersion(km.config.Version)
		if err != nil {
			return fmt.Errorf("invalid kafka version %s: %w", km.config.Version, err)
		}
		saramaConfig.Version = version
	}

	// 配置SASL认证
	if km.config.Auth != nil {
		saramaConfig.Net.SASL.Enable = true
		saramaConfig.Net.SASL.User = km.config.Auth.Username
		saramaConfig.Net.SASL.Password = km.config.Auth.Password

		switch km.config.Auth.Mechanism {
		case "PLAIN":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		case "SCRAM-SHA-256":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
		case "SCRAM-SHA-512":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA512
		default:
			return fmt.Errorf("unsupported SASL mechanism: %s", km.config.Auth.Mechanism)
		}
	}

	// 配置TLS
	if km.config.TLS != nil && km.config.TLS.Enabled {
		saramaConfig.Net.TLS.Enable = true
		tlsConfig := &tls.Config{
			InsecureSkipVerify: km.config.TLS.InsecureSkipVerify,
		}

		// 加载客户端证书
		if km.config.TLS.CertFile != "" && km.config.TLS.KeyFile != "" {
			cert, err := tls.LoadX509KeyPair(km.config.TLS.CertFile, km.config.TLS.KeyFile)
			if err != nil {
				return fmt.Errorf("failed to load client certificate: %w", err)
			}
			tlsConfig.Certificates = []tls.Certificate{cert}
		}

		saramaConfig.Net.TLS.Config = tlsConfig
	}

	// 设置超时
	saramaConfig.Net.DialTimeout = km.timeout
	saramaConfig.Net.ReadTimeout = km.timeout
	saramaConfig.Net.WriteTimeout = km.timeout

	// 创建客户端
	client, err := sarama.NewClient(km.config.Brokers, saramaConfig)
	if err != nil {
		km.isConnected = false
		km.lastError = err
		return fmt.Errorf("failed to create kafka client: %w", err)
	}

	// 创建管理员客户端
	admin, err := sarama.NewClusterAdminFromClient(client)
	if err != nil {
		client.Close()
		km.isConnected = false
		km.lastError = err
		return fmt.Errorf("failed to create kafka admin client: %w", err)
	}

	// 关闭旧连接
	if km.client != nil {
		km.client.Close()
	}
	if km.admin != nil {
		km.admin.Close()
	}

	km.client = client
	km.admin = admin
	km.isConnected = true
	km.lastError = nil
	km.retryCount = 0

	logger.Info("Kafka连接建立成功",
		"brokers", km.config.Brokers,
		"version", km.config.Version)

	return nil
}

// CheckConsumerGroupLag 检查消费者组延迟
func (km *KafkaMonitor) CheckConsumerGroupLag(consumerGroup string) (*ConsumerGroupMetrics, error) {
	km.mu.RLock()
	if !km.isConnected {
		km.mu.RUnlock()
		return nil, fmt.Errorf("kafka client not connected")
	}
	km.mu.RUnlock()

	groupMetrics := &ConsumerGroupMetrics{
		ConsumerGroup:  consumerGroup,
		TopicMetrics:   make(map[string]*TopicLagMetrics),
		ConnectivityOK: true,
	}

	// 获取消费者组信息
	groups, err := km.admin.DescribeConsumerGroups([]string{consumerGroup})
	if err != nil {
		return nil, fmt.Errorf("failed to describe consumer group %s: %w", consumerGroup, err)
	}

	if len(groups) == 0 {
		return nil, fmt.Errorf("consumer group %s not found", consumerGroup)
	}

	groupDetail := groups[0]
	if groupDetail.State != "Stable" {
		logger.Warn("Consumer group is not in stable state",
			"group", consumerGroup,
			"state", groupDetail.State)
	}

	// 获取消费者组的偏移量
	coordinator, err := km.client.Coordinator(consumerGroup)
	if err != nil {
		return nil, fmt.Errorf("failed to get coordinator for group %s: %w", consumerGroup, err)
	}

	request := &sarama.OffsetFetchRequest{
		Version:       1,
		ConsumerGroup: consumerGroup,
	}

	// 获取消费者组实际订阅的主题
	subscribedTopics, err := km.getConsumerGroupTopics(consumerGroup)
	if err != nil {
		return nil, fmt.Errorf("failed to get consumer group topics: %w", err)
	}

	// 如果消费者组没有订阅任何主题，返回空指标
	if len(subscribedTopics) == 0 {
		logger.Warn("消费者组没有订阅任何主题", "group", consumerGroup)
		return groupMetrics, nil
	}

	// 为每个主题创建指标对象
	for _, topic := range subscribedTopics {
		partitions, err := km.client.Partitions(topic)
		if err != nil {
			logger.Warn("获取主题分区失败", "topic", topic, "error", err)
			continue
		}

		topicMetrics := &TopicLagMetrics{
			Topic:           topic,
			ConsumerGroup:   consumerGroup,
			PartitionLags:   make(map[int32]int64),
			ConsumerOffsets: make(map[int32]int64),
			ProducerOffsets: make(map[int32]int64),
			PartitionCount:  len(partitions),
			MaxLagPartition: -1, // 初始化为-1，表示还没有找到最大延迟分区
			ConnectivityOK:  true,
		}

		groupMetrics.TopicMetrics[topic] = topicMetrics

		for _, partition := range partitions {
			request.AddPartition(topic, partition)
		}
	}

	groupMetrics.TopicCount = len(subscribedTopics)

	// 获取消费者偏移量
	response, err := coordinator.FetchOffset(request)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch consumer offsets: %w", err)
	}

	// 计算每个主题的延迟
	for topic, partitionOffsets := range response.Blocks {
		topicMetrics, exists := groupMetrics.TopicMetrics[topic]
		if !exists {
			continue // 跳过不在订阅列表中的主题
		}

		for partition, block := range partitionOffsets {
			if block.Err != sarama.ErrNoError {
				logger.Warn("获取偏移量错误",
					"topic", topic,
					"partition", partition,
					"error", block.Err)
				continue
			}

			consumerOffset := block.Offset
			topicMetrics.ConsumerOffsets[partition] = consumerOffset

			// 获取最新偏移量（生产者偏移量）
			latestOffset, err := km.client.GetOffset(topic, partition, sarama.OffsetNewest)
			if err != nil {
				logger.Warn("获取最新偏移量失败",
					"topic", topic,
					"partition", partition,
					"error", err)
				continue
			}

			topicMetrics.ProducerOffsets[partition] = latestOffset

			// 计算分区延迟
			lag := latestOffset - consumerOffset
			if lag < 0 {
				lag = 0 // 避免负延迟
			}

			topicMetrics.PartitionLags[partition] = lag
			topicMetrics.TopicLag += lag

			// 记录该主题的最大延迟分区
			if lag > 0 && (topicMetrics.MaxLagPartition == -1 || lag > topicMetrics.PartitionLags[topicMetrics.MaxLagPartition]) {
				topicMetrics.MaxLagPartition = partition
			}
		}

		// 累加到总延迟
		groupMetrics.TotalLag += topicMetrics.TopicLag

		logger.Info("KAFKA主题延迟检查完成",
			"group", consumerGroup,
			"topic", topic,
			"lag", topicMetrics.TopicLag,
			"partitions", topicMetrics.PartitionCount,
			"max_lag_partition", topicMetrics.MaxLagPartition)
	}

	return groupMetrics, nil
}

// CheckConsumerGroupLagWithRetry 带重试的消费者组延迟检查
func (km *KafkaMonitor) CheckConsumerGroupLagWithRetry(consumerGroup string) (*ConsumerGroupMetrics, error) {
	var lastErr error

	for attempt := 0; attempt <= km.maxRetries; attempt++ {
		groupMetrics, err := km.CheckConsumerGroupLag(consumerGroup)
		if err == nil {
			km.retryCount = 0 // 重置重试计数
			return groupMetrics, nil
		}

		lastErr = err
		km.retryCount = attempt + 1

		// 如果是连接错误，尝试重新连接
		if !km.isConnected || km.isConnectionError(err) {
			logger.Warn("Kafka连接丢失，尝试重新连接",
				"group", consumerGroup,
				"attempt", attempt+1,
				"error", err)

			if reconnectErr := km.connect(); reconnectErr != nil {
				logger.Error("重新连接Kafka失败",
					"attempt", attempt+1,
					"error", reconnectErr)
			} else {
				// 重连成功，重试当前操作
				continue
			}
		}

		if attempt < km.maxRetries {
			delay := time.Duration(attempt+1) * time.Second
			logger.Debug("Kafka operation failed, retrying",
				"group", consumerGroup,
				"attempt", attempt+1,
				"delay", delay,
				"error", err)
			time.Sleep(delay)
		}
	}

	return nil, fmt.Errorf("kafka operation failed after %d retries: %w", km.maxRetries+1, lastErr)
}

// GetPartitionLag 获取特定分区的延迟
func (km *KafkaMonitor) GetPartitionLag(topic string, partition int32, consumerGroup string) (int64, error) {
	km.mu.RLock()
	if !km.isConnected {
		km.mu.RUnlock()
		return 0, fmt.Errorf("kafka client not connected")
	}
	km.mu.RUnlock()

	// 获取消费者偏移量
	coordinator, err := km.client.Coordinator(consumerGroup)
	if err != nil {
		return 0, fmt.Errorf("failed to get coordinator: %w", err)
	}

	request := &sarama.OffsetFetchRequest{
		Version:       1,
		ConsumerGroup: consumerGroup,
	}
	request.AddPartition(topic, partition)

	response, err := coordinator.FetchOffset(request)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch offset: %w", err)
	}

	block, exists := response.Blocks[topic][partition]
	if !exists {
		return 0, fmt.Errorf("partition %d not found in topic %s", partition, topic)
	}

	if block.Err != sarama.ErrNoError {
		return 0, fmt.Errorf("error fetching offset: %v", block.Err)
	}

	consumerOffset := block.Offset

	// 获取最新偏移量
	latestOffset, err := km.client.GetOffset(topic, partition, sarama.OffsetNewest)
	if err != nil {
		return 0, fmt.Errorf("failed to get latest offset: %w", err)
	}

	lag := latestOffset - consumerOffset
	if lag < 0 {
		lag = 0
	}

	return lag, nil
}

// getConsumerGroupTopics 获取消费者组订阅的主题列表
func (km *KafkaMonitor) getConsumerGroupTopics(consumerGroup string) ([]string, error) {
	// 获取消费者组的详细信息
	groups, err := km.admin.DescribeConsumerGroups([]string{consumerGroup})
	if err != nil {
		return nil, fmt.Errorf("failed to describe consumer group %s: %w", consumerGroup, err)
	}

	if len(groups) == 0 {
		return nil, fmt.Errorf("consumer group %s not found", consumerGroup)
	}

	groupDetail := groups[0]

	// 检查消费者组状态，如果不是稳定状态，记录警告
	if groupDetail.State != "Stable" {
		logger.Warn("消费者组状态不稳定，可能影响主题获取",
			"group", consumerGroup,
			"state", groupDetail.State)
	}

	// 从消费者组成员中提取订阅的主题
	topicSet := make(map[string]bool)

	for _, member := range groupDetail.Members {
		// 解析成员的订阅信息
		if member.MemberAssignment != nil {
			assignment, err := member.GetMemberAssignment()
			if err != nil {
				logger.Warn("解析成员分配失败",
					"group", consumerGroup,
					"member", member.MemberId,
					"error", err)
				continue
			}

			// 检查assignment和Topics是否为nil，避免空指针解引用
			if assignment != nil && assignment.Topics != nil {
				// 添加分配的主题
				for topic := range assignment.Topics {
					topicSet[topic] = true
				}
			} else {
				logger.Debug("成员分配为空或Topics为nil",
					"group", consumerGroup,
					"member", member.MemberId)
			}
		}
	}

	// 如果从成员分配中没有获取到主题，尝试从偏移量中获取
	if len(topicSet) == 0 {
		logger.Debug("从成员分配中未找到主题，尝试从偏移量获取", "group", consumerGroup)
		return km.getTopicsFromOffsets(consumerGroup)
	}

	// 转换为切片
	topics := make([]string, 0, len(topicSet))
	for topic := range topicSet {
		topics = append(topics, topic)
	}

	logger.Debug("Found subscribed topics for consumer group",
		"group", consumerGroup,
		"topics", topics)

	return topics, nil
}

// getTopicsFromOffsets 从偏移量信息中获取消费者组的主题（备用方法）
func (km *KafkaMonitor) getTopicsFromOffsets(consumerGroup string) ([]string, error) {
	// 获取所有主题
	allTopics, err := km.client.Topics()
	if err != nil {
		return nil, fmt.Errorf("failed to get all topics: %w", err)
	}

	coordinator, err := km.client.Coordinator(consumerGroup)
	if err != nil {
		return nil, fmt.Errorf("failed to get coordinator: %w", err)
	}

	var subscribedTopics []string

	// 检查每个主题是否有该消费者组的偏移量
	for _, topic := range allTopics {
		partitions, err := km.client.Partitions(topic)
		if err != nil {
			continue
		}

		// 检查第一个分区是否有偏移量
		if len(partitions) > 0 {
			request := &sarama.OffsetFetchRequest{
				Version:       1,
				ConsumerGroup: consumerGroup,
			}
			request.AddPartition(topic, partitions[0])

			response, err := coordinator.FetchOffset(request)
			if err != nil {
				logger.Debug("获取偏移量失败，跳过主题",
					"group", consumerGroup,
					"topic", topic,
					"error", err)
				continue
			}

			// 检查响应是否为空
			if response == nil || response.Blocks == nil {
				logger.Debug("偏移量响应为空，跳过主题",
					"group", consumerGroup,
					"topic", topic)
				continue
			}

			if topicBlocks, topicExists := response.Blocks[topic]; topicExists && topicBlocks != nil {
				if block, partitionExists := topicBlocks[partitions[0]]; partitionExists {
					// 如果偏移量不是-1，说明该消费者组订阅了这个主题
					if block.Offset != -1 {
						subscribedTopics = append(subscribedTopics, topic)
					}
				}
			}
		}
	}

	logger.Debug("从偏移量中找到订阅的主题",
		"group", consumerGroup,
		"topics", subscribedTopics)

	return subscribedTopics, nil
}

// IsConnected 检查连接状态
func (km *KafkaMonitor) IsConnected() bool {
	km.mu.RLock()
	defer km.mu.RUnlock()
	return km.isConnected
}

// GetLastError 获取最后一次错误
func (km *KafkaMonitor) GetLastError() error {
	km.mu.RLock()
	defer km.mu.RUnlock()
	return km.lastError
}

// GetRetryCount 获取当前重试次数
func (km *KafkaMonitor) GetRetryCount() int {
	km.mu.RLock()
	defer km.mu.RUnlock()
	return km.retryCount
}

// isConnectionError 判断是否为连接错误
func (km *KafkaMonitor) isConnectionError(err error) bool {
	if err == nil {
		return false
	}

	// 检查常见的连接错误
	errStr := err.Error()
	connectionErrors := []string{
		"connection refused",
		"no such host",
		"timeout",
		"network is unreachable",
		"connection reset",
		"broken pipe",
		"EOF",
	}

	for _, connErr := range connectionErrors {
		if contains(errStr, connErr) {
			return true
		}
	}

	return false
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					findSubstring(s, substr))))
}

// findSubstring 在字符串中查找子字符串
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// Close 关闭Kafka连接
func (km *KafkaMonitor) Close() error {
	km.mu.Lock()
	defer km.mu.Unlock()

	// 如果已经关闭，直接返回
	if !km.isConnected && km.client == nil && km.admin == nil {
		logger.Debug("Kafka监控器已经关闭，跳过重复关闭")
		return nil
	}

	var errs []error

	if km.admin != nil {
		if err := km.admin.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭管理客户端失败: %w", err))
		}
		km.admin = nil
	}

	if km.client != nil {
		if err := km.client.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭客户端失败: %w", err))
		}
		km.client = nil
	}

	km.isConnected = false

	if len(errs) > 0 {
		return fmt.Errorf("关闭Kafka监控器时出错: %v", errs)
	}

	logger.Info("Kafka监控器关闭成功")
	return nil
}
