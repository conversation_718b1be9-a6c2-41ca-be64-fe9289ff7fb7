package service

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/shared/logger"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// MessageBucket 消息时间桶（按秒聚合）
type MessageBucket struct {
	Timestamp time.Time // 桶的时间戳（精确到秒）
	Count     int64     // 该秒内的消息总数
	Bytes     int64     // 该秒内的字节总数
}

// TopicMessageCounter 主题消息计数器
type TopicMessageCounter struct {
	Topic         string
	MessageCount  int64
	ByteCount     int64
	LastResetTime time.Time
	// 滑动窗口记录（按秒聚合的桶）
	Buckets []MessageBucket
	mu      sync.RWMutex
}

// EMQXClient EMQX客户端（基于MQTT订阅）
type EMQXClient struct {
	config        *config.EMQXConfig
	mqttClient    mqtt.Client
	mu            sync.RWMutex
	mqttConnected bool
	lastError     error
	retryCount    int
	maxRetries    int
	timeout       time.Duration

	// 主题消息计数器
	topicCounters map[string]*TopicMessageCounter
	countersMu    sync.RWMutex

	// 清理控制
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// TopicStats EMQX主题统计信息
type TopicStats struct {
	Topic       string  `json:"topic"`        // 主题名称
	MessageRate float64 `json:"message_rate"` // 消息速率（消息/秒）
	ByteRate    float64 `json:"byte_rate"`    // 字节速率（字节/秒）
	MessageIn   int64   `json:"message_in"`   // 入站消息总数
	MessageOut  int64   `json:"message_out"`  // 出站消息总数
	ByteIn      int64   `json:"byte_in"`      // 入站字节总数
	ByteOut     int64   `json:"byte_out"`     // 出站字节总数
}

// NewEMQXClient 创建EMQX客户端实例
func NewEMQXClient(emqxConfig *config.EMQXConfig) (*EMQXClient, error) {
	if emqxConfig == nil {
		return nil, fmt.Errorf("emqx config cannot be nil")
	}

	if emqxConfig.Host == "" {
		return nil, fmt.Errorf("emqx host cannot be empty")
	}

	client := &EMQXClient{
		config:        emqxConfig,
		maxRetries:    emqxConfig.RetryCount,
		timeout:       time.Duration(emqxConfig.Timeout) * time.Second,
		topicCounters: make(map[string]*TopicMessageCounter),
		stopCleanup:   make(chan struct{}),
	}

	// 初始化MQTT客户端
	if err := client.initMQTTClient(); err != nil {
		return nil, fmt.Errorf("failed to initialize MQTT client: %w", err)
	}

	// 连接MQTT服务器
	if err := client.ConnectMQTT(); err != nil {
		return nil, fmt.Errorf("failed to connect to mqtt broker: %w", err)
	}

	// 启动定期清理协程
	client.startCleanupRoutine()

	return client, nil
}

// GetTopicStats 获取主题统计信息（通过MQTT订阅方式）
func (c *EMQXClient) GetTopicStats(topic string) (*TopicStats, error) {
	// 确保已订阅该主题
	c.countersMu.RLock()
	_, exists := c.topicCounters[topic]
	c.countersMu.RUnlock()

	if !exists {
		// 首次访问该主题，开始订阅
		if err := c.subscribeToTopic(topic); err != nil {
			logger.Warn("订阅主题进行监控失败", "topic", topic, "error", err)
			// 订阅失败时返回零值统计，但不报错，让监控继续运行
			return &TopicStats{
				Topic:       topic,
				MessageRate: 0,
				ByteRate:    0,
				MessageIn:   0,
				MessageOut:  0,
				ByteIn:      0,
				ByteOut:     0,
			}, nil
		}

		logger.Info("通过MQTT订阅开始监控主题", "topic", topic)

		// 新订阅的主题，返回零值统计
		return &TopicStats{
			Topic:       topic,
			MessageRate: 0,
			ByteRate:    0,
			MessageIn:   0,
			MessageOut:  0,
			ByteIn:      0,
			ByteOut:     0,
		}, nil
	}

	// 获取主题消息速率和计数（使用配置的速率窗口）
	rateWindow := c.config.RateWindow
	if rateWindow <= 0 {
		rateWindow = 60 // 默认60秒窗口
	}
	messageRate, totalMessages, totalBytes := c.getTopicMessageRate(topic, rateWindow)

	// 计算平均字节速率
	var byteRate float64
	if totalMessages > 0 {
		avgMessageSize := float64(totalBytes) / float64(totalMessages)
		byteRate = messageRate * avgMessageSize
	}

	logger.Debug("主题统计信息已计算",
		"topic", topic,
		"message_rate", messageRate,
		"byte_rate", byteRate,
		"total_messages", totalMessages,
		"total_bytes", totalBytes)

	return &TopicStats{
		Topic:       topic,
		MessageRate: messageRate,
		ByteRate:    byteRate,
		MessageIn:   totalMessages, // 总接收消息数
		MessageOut:  0,             // MQTT订阅只能统计接收的消息
		ByteIn:      totalBytes,    // 总接收字节数
		ByteOut:     0,             // MQTT订阅只能统计接收的字节
	}, nil
}

// GetTopicStatsWithRetry 带重试的主题统计获取
func (c *EMQXClient) GetTopicStatsWithRetry(topic string) (*TopicStats, error) {
	var lastErr error

	for attempt := 0; attempt <= c.maxRetries; attempt++ {
		stats, err := c.GetTopicStats(topic)
		if err == nil {
			c.retryCount = 0 // 重置重试计数
			return stats, nil
		}

		lastErr = err
		c.retryCount = attempt + 1

		// 如果是连接错误，尝试重新连接
		if !c.mqttConnected || c.isConnectionError(err) {
			logger.Warn("MQTT连接丢失，尝试重新连接",
				"topic", topic,
				"attempt", attempt+1,
				"error", err)

			if reconnectErr := c.ConnectMQTT(); reconnectErr != nil {
				logger.Error("重新连接MQTT失败",
					"attempt", attempt+1,
					"error", reconnectErr)
			} else {
				// 重连成功，重试当前操作
				continue
			}
		}

		if attempt < c.maxRetries {
			delay := time.Duration(attempt+1) * time.Second
			logger.Debug("EMQX操作失败，正在重试",
				"topic", topic,
				"attempt", attempt+1,
				"delay", delay,
				"error", err)
			time.Sleep(delay)
		}
	}

	return nil, fmt.Errorf("重试%d次后获取主题统计失败: %w", c.maxRetries+1, lastErr)
}

// isConnectionError 判断是否为连接错误
func (c *EMQXClient) isConnectionError(err error) bool {
	if err == nil {
		return false
	}

	// 检查常见的连接错误
	errStr := err.Error()
	return strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "no such host") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "network is unreachable") ||
		strings.Contains(errStr, "connection reset") ||
		strings.Contains(errStr, "connectex")
}

// Close 关闭客户端连接
func (c *EMQXClient) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 停止清理协程
	if c.stopCleanup != nil {
		close(c.stopCleanup)
	}

	// 停止清理定时器
	if c.cleanupTicker != nil {
		c.cleanupTicker.Stop()
	}

	if c.mqttClient != nil && c.mqttClient.IsConnected() {
		c.mqttClient.Disconnect(250)
	}
	c.mqttConnected = false
	logger.Info("EMQX客户端已关闭")
	return nil
}

// IsConnected 检查连接状态
func (c *EMQXClient) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.mqttConnected
}

// GetLastError 获取最后一次错误
func (c *EMQXClient) GetLastError() error {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lastError
}

// GetExistingTopicMetrics 获取已监控的主题列表
func (c *EMQXClient) GetExistingTopicMetrics() ([]string, error) {
	c.countersMu.RLock()
	defer c.countersMu.RUnlock()

	var topics []string
	for topic := range c.topicCounters {
		topics = append(topics, topic)
	}

	return topics, nil
}

// initMQTTClient 初始化MQTT客户端
func (c *EMQXClient) initMQTTClient() error {
	// 创建MQTT客户端选项
	opts := mqtt.NewClientOptions()

	// 设置MQTT连接参数
	// 如果配置了端口，使用配置的端口，否则使用默认MQTT端口1883
	mqttPort := 1883
	if c.config.Port != 0 {
		mqttPort = c.config.Port
	}
	mqttURL := fmt.Sprintf("tcp://%s:%d", c.config.Host, mqttPort)
	opts.AddBroker(mqttURL)

	logger.Info("初始化MQTT客户端连接",
		"host", c.config.Host,
		"port", mqttPort,
		"url", mqttURL)

	// 设置客户端ID
	clientID := fmt.Sprintf("monitor_client_%d", time.Now().Unix())
	opts.SetClientID(clientID)

	// 设置认证信息
	if c.config.Auth != nil {
		if c.config.Auth.Username != "" && c.config.Auth.Password != "" {
			opts.SetUsername(c.config.Auth.Username)
			opts.SetPassword(c.config.Auth.Password)
		}
	}

	// 设置连接参数
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetConnectTimeout(time.Duration(c.config.Timeout) * time.Second)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(10 * time.Second)

	// 设置连接丢失处理
	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		logger.Warn("MQTT连接丢失", "error", err)
		c.mu.Lock()
		c.mqttConnected = false
		c.mu.Unlock()
	})

	// 设置连接成功处理
	opts.SetOnConnectHandler(func(client mqtt.Client) {
		logger.Info("MQTT连接已建立")
		c.mu.Lock()
		c.mqttConnected = true
		c.mu.Unlock()
	})

	// 创建MQTT客户端
	c.mqttClient = mqtt.NewClient(opts)

	return nil
}

// ConnectMQTT 连接MQTT服务器（公开方法）
func (c *EMQXClient) ConnectMQTT() error {
	if c.mqttClient == nil {
		return fmt.Errorf("MQTT client not initialized")
	}

	if token := c.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to connect to MQTT broker: %w", token.Error())
	}

	// 等待连接状态确认，最多等待连接超时时间
	timeout := time.After(c.timeout)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待MQTT连接确认超时")
		case <-ticker.C:
			c.mu.RLock()
			connected := c.mqttConnected
			c.mu.RUnlock()
			if connected {
				logger.Info("MQTT连接状态确认成功")
				return nil
			}
		}
	}
}

// subscribeToTopic 订阅主题并开始计数
func (c *EMQXClient) subscribeToTopic(topic string) error {
	if !c.mqttConnected {
		if err := c.ConnectMQTT(); err != nil {
			return fmt.Errorf("failed to connect MQTT: %w", err)
		}
	}

	// 初始化主题计数器
	c.countersMu.Lock()
	if _, exists := c.topicCounters[topic]; !exists {
		c.topicCounters[topic] = &TopicMessageCounter{
			Topic:         topic,
			MessageCount:  0,
			ByteCount:     0,
			LastResetTime: time.Now(),
		}
	}
	counter := c.topicCounters[topic]
	c.countersMu.Unlock()

	// 订阅主题
	token := c.mqttClient.Subscribe(topic, 0, func(client mqtt.Client, msg mqtt.Message) {
		now := time.Now()
		messageSize := int64(len(msg.Payload()))

		// 更新消息计数
		counter.mu.Lock()
		counter.MessageCount++
		counter.ByteCount += messageSize

		// 添加到时间桶（按配置的间隔聚合）
		bucketInterval := c.config.BucketInterval
		if bucketInterval <= 0 {
			bucketInterval = 1 // 默认1秒
		}
		bucketDuration := time.Duration(bucketInterval) * time.Second
		bucketTime := now.Truncate(bucketDuration)

		// 查找或创建当前秒的桶
		bucketIndex := -1
		for i := len(counter.Buckets) - 1; i >= 0; i-- {
			if counter.Buckets[i].Timestamp.Equal(bucketTime) {
				bucketIndex = i
				break
			}
		}

		if bucketIndex >= 0 {
			// 更新现有桶
			counter.Buckets[bucketIndex].Count++
			counter.Buckets[bucketIndex].Bytes += messageSize
		} else {
			// 创建新桶
			counter.Buckets = append(counter.Buckets, MessageBucket{
				Timestamp: bucketTime,
				Count:     1,
				Bytes:     messageSize,
			})
		}
		counter.mu.Unlock()

		logger.Debug("Received message on monitored topic",
			"topic", topic,
			"size", len(msg.Payload()),
			"total_count", counter.MessageCount)
	})

	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to subscribe to topic %s: %w", topic, token.Error())
	}

	logger.Info("Successfully subscribed to topic for monitoring", "topic", topic)
	return nil
}

// getTopicMessageRate 获取主题消息速率（使用时间桶滑动窗口）
func (c *EMQXClient) getTopicMessageRate(topic string, windowSeconds int) (float64, int64, int64) {
	c.countersMu.RLock()
	counter, exists := c.topicCounters[topic]
	c.countersMu.RUnlock()

	if !exists {
		return 0, 0, 0
	}

	now := time.Now()
	windowDuration := time.Duration(windowSeconds) * time.Second
	cutoffTime := now.Add(-windowDuration)

	counter.mu.Lock()
	defer counter.mu.Unlock()

	// 清理过期的时间桶
	validBuckets := make([]MessageBucket, 0, len(counter.Buckets))
	for _, bucket := range counter.Buckets {
		if bucket.Timestamp.After(cutoffTime) {
			validBuckets = append(validBuckets, bucket)
		}
	}
	counter.Buckets = validBuckets

	// 如果没有有效桶，返回0
	if len(counter.Buckets) == 0 {
		return 0, counter.MessageCount, counter.ByteCount
	}

	// 计算窗口内的总消息数和字节数
	var totalMessages, totalBytes int64
	for _, bucket := range counter.Buckets {
		totalMessages += bucket.Count
		totalBytes += bucket.Bytes
	}

	// 计算实际时间窗口
	oldestBucket := counter.Buckets[0]
	actualWindow := now.Sub(oldestBucket.Timestamp).Seconds()

	if actualWindow <= 0 {
		return 0, counter.MessageCount, counter.ByteCount
	}

	// 计算速率
	messageRate := float64(totalMessages) / actualWindow

	return messageRate, counter.MessageCount, counter.ByteCount
}

// startCleanupRoutine 启动定期清理协程
func (c *EMQXClient) startCleanupRoutine() {
	// 每30秒清理一次过期桶
	c.cleanupTicker = time.NewTicker(30 * time.Second)

	go func() {
		defer c.cleanupTicker.Stop()

		for {
			select {
			case <-c.cleanupTicker.C:
				c.cleanupExpiredBuckets()
			case <-c.stopCleanup:
				return
			}
		}
	}()

	logger.Info("EMQX客户端清理协程已启动", "cleanup_interval", "30s")
}

// cleanupExpiredBuckets 清理所有主题的过期桶
func (c *EMQXClient) cleanupExpiredBuckets() {
	c.countersMu.RLock()
	topics := make([]string, 0, len(c.topicCounters))
	for topic := range c.topicCounters {
		topics = append(topics, topic)
	}
	c.countersMu.RUnlock()

	now := time.Now()
	// 保留最大窗口时间的2倍数据，确保不会误删
	maxWindow := c.config.RateWindow
	if c.config.BaselineWindow > maxWindow {
		maxWindow = c.config.BaselineWindow
	}
	cutoffTime := now.Add(-time.Duration(maxWindow*2) * time.Second)

	cleanedCount := 0
	for _, topic := range topics {
		c.countersMu.RLock()
		counter, exists := c.topicCounters[topic]
		c.countersMu.RUnlock()

		if !exists {
			continue
		}

		counter.mu.Lock()
		originalCount := len(counter.Buckets)

		// 清理过期桶
		validBuckets := make([]MessageBucket, 0, len(counter.Buckets))
		for _, bucket := range counter.Buckets {
			if bucket.Timestamp.After(cutoffTime) {
				validBuckets = append(validBuckets, bucket)
			}
		}
		counter.Buckets = validBuckets

		cleaned := originalCount - len(counter.Buckets)
		cleanedCount += cleaned
		counter.mu.Unlock()

		if cleaned > 0 {
			logger.Debug("清理主题过期桶",
				"topic", topic,
				"cleaned_buckets", cleaned,
				"remaining_buckets", len(counter.Buckets))
		}
	}

	if cleanedCount > 0 {
		logger.Info("定期清理过期桶完成",
			"total_cleaned", cleanedCount,
			"cutoff_time", cutoffTime.Format("15:04:05"))
	}
}

// resetTopicCounter 重置主题计数器
func (c *EMQXClient) resetTopicCounter(topic string) {
	c.countersMu.Lock()
	defer c.countersMu.Unlock()

	if counter, exists := c.topicCounters[topic]; exists {
		counter.mu.Lock()
		counter.MessageCount = 0
		counter.ByteCount = 0
		counter.LastResetTime = time.Now()
		counter.mu.Unlock()
	}
}
