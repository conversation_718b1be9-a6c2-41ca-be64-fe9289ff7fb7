package service

import (
	"fmt"
	"sync"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/shared/logger"
)

// ContainerManager 容器管理器（仅使用Agent）
type ContainerManager struct {
	remoteDockerService *RemoteDockerService
	restartCooldown     map[string]time.Time // 容器重启冷却期
	restartCount        map[string]int       // 容器重启次数
	mu                  sync.RWMutex         // 保护map并发访问的读写锁
}

// NewContainerManager 创建容器管理器
func NewContainerManager() (*ContainerManager, error) {
	remoteDockerService := NewRemoteDockerService()

	return &ContainerManager{
		remoteDockerService: remoteDockerService,
		restartCooldown:     make(map[string]time.Time),
		restartCount:        make(map[string]int),
	}, nil
}

// Close 关闭容器管理器
func (cm *ContainerManager) Close() error {
	// Agent服务不需要关闭连接
	return nil
}

// HandleServiceFailure 处理服务失败，尝试通过Agent重启容器
func (cm *ContainerManager) HandleServiceFailure(serviceConfig config.ServiceConfig) error {
	if serviceConfig.Docker == nil {
		return fmt.Errorf("服务 %s 的Docker配置不可用", serviceConfig.Name)
	}

	if serviceConfig.Docker.Agent == nil {
		return fmt.Errorf("服务 %s 的Agent配置不可用", serviceConfig.Name)
	}

	containerName := serviceConfig.Docker.ContainerName

	// 检查是否在冷却期内
	if cm.isInCooldown(containerName, serviceConfig.Docker.RestartCooldown) {
		logger.Debug("容器重启处于冷却期",
			"container", containerName)
		return fmt.Errorf("容器 %s 处于重启冷却期", containerName)
	}

	// 检查重启次数是否超过限制
	cm.mu.RLock()
	currentCount := cm.restartCount[containerName]
	cm.mu.RUnlock()

	if currentCount >= serviceConfig.Docker.MaxRestartCount {
		logger.Warn("容器重启次数超过最大限制",
			"container", containerName,
			"count", currentCount,
			"max", serviceConfig.Docker.MaxRestartCount)
		return fmt.Errorf("容器 %s 重启次数超过最大限制 (%d)",
			containerName, serviceConfig.Docker.MaxRestartCount)
	}

	// 使用Agent重启容器
	logger.Info("使用代理重启容器",
		"service", serviceConfig.Name,
		"container", containerName,
		"agent", fmt.Sprintf("%s:%d", serviceConfig.Docker.Agent.Host, serviceConfig.Docker.Agent.Port))

	err := cm.remoteDockerService.RestartContainer(serviceConfig)
	if err != nil {
		return fmt.Errorf("通过Agent重启容器失败: %w", err)
	}

	// 更新重启记录
	cm.mu.Lock()
	cm.restartCount[containerName]++
	cm.restartCooldown[containerName] = time.Now()
	newCount := cm.restartCount[containerName]
	cm.mu.Unlock()

	logger.Info("通过代理完成容器重启",
		"service", serviceConfig.Name,
		"container", containerName,
		"restart_count", newCount)

	return nil
}

// isInCooldown 检查是否在冷却期内
func (cm *ContainerManager) isInCooldown(containerName, cooldownStr string) bool {
	cm.mu.RLock()
	lastRestart, exists := cm.restartCooldown[containerName]
	cm.mu.RUnlock()

	if !exists {
		return false
	}

	cooldown, err := time.ParseDuration(cooldownStr)
	if err != nil {
		logger.Warn("无效的冷却时间", "duration", cooldownStr, "error", err)
		cooldown = 5 * time.Minute // 默认5分钟
	}

	return time.Since(lastRestart) < cooldown
}

// ResetRestartCount 重置重启计数（当服务恢复健康时调用）
func (cm *ContainerManager) ResetRestartCount(containerName string) {
	cm.mu.Lock()
	delete(cm.restartCount, containerName)
	delete(cm.restartCooldown, containerName)
	cm.mu.Unlock()
	logger.Debug("重置容器重启计数", "container", containerName)
}

// GetRestartInfo 获取重启信息
func (cm *ContainerManager) GetRestartInfo(containerName string) (int, time.Time, bool) {
	cm.mu.RLock()
	count := cm.restartCount[containerName]
	lastRestart := cm.restartCooldown[containerName]
	cm.mu.RUnlock()
	return count, lastRestart, !lastRestart.IsZero()
}

// GetContainerStatus 获取容器状态（用于预检查）
func (cm *ContainerManager) GetContainerStatus(serviceConfig config.ServiceConfig) (*AgentContainerStatus, error) {
	return cm.remoteDockerService.GetContainerStatus(serviceConfig)
}
