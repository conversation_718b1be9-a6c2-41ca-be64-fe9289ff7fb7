package service

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/model"
	"monitor/internal/server/repository"
	"monitor/internal/shared/logger"
)

// EMQXMonitor EMQX监控器
type EMQXMonitor struct {
	config      *config.EMQXConfig
	client      *EMQXClient
	metricsRepo *repository.MetricsHistoryRepository
	serviceName string
	mu          sync.RWMutex
	isConnected bool
	lastError   error
	retryCount  int
	maxRetries  int
	timeout     time.Duration
}

// TopicRateMetrics 主题速率指标
type TopicRateMetrics struct {
	Topic          string    `json:"topic"`           // 主题名称
	CurrentRate    float64   `json:"current_rate"`    // 当前消息速率（消息/秒）
	BaselineRate   float64   `json:"baseline_rate"`   // 基线速率（消息/秒）
	RatePct        float64   `json:"rate_pct"`        // 当前速率占基线速率的百分比
	MessageIn      int64     `json:"message_in"`      // 入站消息总数
	MessageOut     int64     `json:"message_out"`     // 出站消息总数
	ByteIn         int64     `json:"byte_in"`         // 入站字节总数
	ByteOut        int64     `json:"byte_out"`        // 出站字节总数
	Timestamp      time.Time `json:"timestamp"`       // 采集时间
	ConnectivityOK bool      `json:"connectivity_ok"` // 连接状态
}

// NewEMQXMonitor 创建EMQX监控实例
func NewEMQXMonitor(serviceName string, emqxConfig *config.EMQXConfig, metricsRepo *repository.MetricsHistoryRepository) (*EMQXMonitor, error) {
	if emqxConfig == nil {
		return nil, fmt.Errorf("emqx config cannot be nil")
	}

	if metricsRepo == nil {
		return nil, fmt.Errorf("metrics repository cannot be nil")
	}

	if serviceName == "" {
		return nil, fmt.Errorf("service name cannot be empty")
	}

	// 创建EMQX客户端
	client, err := NewEMQXClient(emqxConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create emqx client: %w", err)
	}

	monitor := &EMQXMonitor{
		config:      emqxConfig,
		client:      client,
		metricsRepo: metricsRepo,
		serviceName: serviceName,
		maxRetries:  emqxConfig.RetryCount,
		timeout:     time.Duration(emqxConfig.Timeout) * time.Second,
		isConnected: client.IsConnected(),
	}

	logger.Info("EMQX监控器创建成功",
		"service", serviceName,
		"host", emqxConfig.Host,
		"port", emqxConfig.Port,
		"topics", emqxConfig.Topics)

	return monitor, nil
}

// CheckTopicRates 检查所有主题的速率指标
func (m *EMQXMonitor) CheckTopicRates() ([]*TopicRateMetrics, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	var allMetrics []*TopicRateMetrics
	now := time.Now()

	// 检查连接状态，如果未连接则尝试重连
	if !m.client.IsConnected() {
		logger.Warn("EMQX客户端未连接，尝试重新连接", "service", m.serviceName)

		// 尝试重新连接
		if err := m.client.ConnectMQTT(); err != nil {
			m.isConnected = false
			m.lastError = err
			return nil, fmt.Errorf("EMQX客户端重连失败: %w", err)
		}

		logger.Info("EMQX客户端重连成功", "service", m.serviceName)
	}

	// 遍历所有配置的主题
	for _, topic := range m.config.Topics {
		metrics, err := m.checkSingleTopicRate(topic, now)
		if err != nil {
			logger.Error("检查主题速率失败",
				"service", m.serviceName,
				"topic", topic,
				"error", err)

			// 创建错误指标
			metrics = &TopicRateMetrics{
				Topic:          topic,
				CurrentRate:    0,
				BaselineRate:   0,
				RatePct:        0,
				Timestamp:      now,
				ConnectivityOK: false,
			}
		}

		allMetrics = append(allMetrics, metrics)
	}

	m.isConnected = len(allMetrics) > 0
	return allMetrics, nil
}

// checkSingleTopicRate 检查单个主题的速率指标
func (m *EMQXMonitor) checkSingleTopicRate(topic string, timestamp time.Time) (*TopicRateMetrics, error) {
	// 获取主题统计信息（包含实时计算的速率）
	stats, err := m.client.GetTopicStatsWithRetry(topic)
	if err != nil {
		return nil, fmt.Errorf("获取主题统计信息失败: %w", err)
	}

	// 使用EMQXClient中已经计算好的实时速率
	currentRate := stats.MessageRate

	// 计算基线速率
	baselineRate, err := m.calculateBaseline(topic, timestamp)
	if err != nil {
		logger.Warn("计算基线速率失败，使用当前速率",
			"topic", topic,
			"error", err)
		baselineRate = currentRate
	}

	// 计算当前速率占基线速率的百分比
	ratePct := m.calculateRatePercentage(currentRate, baselineRate)

	metrics := &TopicRateMetrics{
		Topic:          topic,
		CurrentRate:    currentRate,
		BaselineRate:   baselineRate,
		RatePct:        ratePct,
		MessageIn:      stats.MessageIn,
		MessageOut:     stats.MessageOut,
		ByteIn:         stats.ByteIn,
		ByteOut:        stats.ByteOut,
		Timestamp:      timestamp,
		ConnectivityOK: true,
	}

	logger.Info("EMQX主题速率检查完成",
		"服务", m.serviceName,
		"主题", topic,
		"当前窗口速率", currentRate,
		"基线速率", baselineRate,
		"当前速率占基线百分比", ratePct)

	return metrics, nil
}

// calculateBaseline 计算基线速率（移动平均）
func (m *EMQXMonitor) calculateBaseline(topic string, timestamp time.Time) (float64, error) {
	// 获取基线计算窗口内的历史数据
	startTime := timestamp.Add(-time.Duration(m.config.BaselineWindow) * time.Second)

	stats, err := m.metricsRepo.GetMetricStats(
		m.serviceName,
		model.MetricEMQXCurrentRate,
		startTime,
		timestamp,
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get baseline stats: %w", err)
	}

	// 获取平均值
	if average, ok := stats["average"].(float64); ok {
		// 应用最小基线速率保护
		if average < float64(m.config.MinBaselineRate) {
			return float64(m.config.MinBaselineRate), nil
		}
		return average, nil
	}

	return float64(m.config.MinBaselineRate), nil
}

// calculateRatePercentage 计算当前速率占基线速率的百分比
func (m *EMQXMonitor) calculateRatePercentage(currentRate, baselineRate float64) float64 {
	if baselineRate <= 0 {
		return 100 // 如果没有基线，认为是100%（正常状态）
	}

	remainingPct := (currentRate / baselineRate) * 100
	if remainingPct > 100 {
		remainingPct = 100 // 速率超过基线的情况，限制为100%
	}

	return remainingPct
}

// isTopicMatch 检查指标的Tags是否匹配指定主题
func (m *EMQXMonitor) isTopicMatch(tags, topic string) bool {
	if tags == "" {
		return false
	}

	// 尝试解析JSON格式的Tags
	var tagMap map[string]interface{}
	if err := json.Unmarshal([]byte(tags), &tagMap); err == nil {
		if topicValue, exists := tagMap["topic"]; exists {
			if topicStr, ok := topicValue.(string); ok {
				return topicStr == topic
			}
		}
	}

	// 回退到字符串匹配
	return strings.Contains(tags, fmt.Sprintf(`"topic":"%s"`, topic))
}

// Close 关闭监控器
func (m *EMQXMonitor) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 如果已经关闭，直接返回
	if !m.isConnected && m.client == nil {
		logger.Debug("EMQX监控器已经关闭，跳过重复关闭", "service", m.serviceName)
		return nil
	}

	if m.client != nil {
		if err := m.client.Close(); err != nil {
			logger.Error("关闭EMQX客户端失败", "service", m.serviceName, "error", err)
			return err
		}
		m.client = nil
	}

	m.isConnected = false
	logger.Info("EMQX客户端已关闭", "service", m.serviceName)
	return nil
}

// IsConnected 检查连接状态
func (m *EMQXMonitor) IsConnected() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isConnected && m.client != nil && m.client.IsConnected()
}

// GetLastError 获取最后一次错误
func (m *EMQXMonitor) GetLastError() error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.lastError
}
