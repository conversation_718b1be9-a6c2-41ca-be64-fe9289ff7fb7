package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"monitor/internal/server/config"
	"monitor/internal/server/model"
	"monitor/internal/server/repository"
	"monitor/internal/server/service"
	"monitor/internal/shared/logger"

	"github.com/gin-gonic/gin"
)

// Handler HTTP处理器
type Handler struct {
	config            *config.Config
	alertRepo         *repository.AlertRepository
	serviceHealthRepo *repository.ServiceHealthRepository
	notificationRepo  *repository.NotificationRepository
	smsNotifier       *service.SMSNotifier
}

// SMSTestRequest 短信测试请求结构
type SMSTestRequest struct {
	Phone   string `json:"phone" binding:"required"`   // 手机号码
	Content string `json:"content" binding:"required"` // 短信内容
}

// SMSTestResponse 短信测试响应结构
type SMSTestResponse struct {
	Success   bool   `json:"success"`          // 发送是否成功
	Message   string `json:"message"`          // 响应消息
	SMSID     string `json:"sms_id,omitempty"` // 短信ID（成功时返回）
	Timestamp string `json:"timestamp"`        // 发送时间戳
}

// ServiceWithInstances 包含实例信息的服务响应结构
type ServiceWithInstances struct {
	ServiceName string                `json:"service_name"` // 服务名称
	Instances   []model.ServiceHealth `json:"instances"`    // 实例列表
	Summary     ServiceSummary        `json:"summary"`      // 服务汇总信息
}

// ServiceSummary 服务汇总信息
type ServiceSummary struct {
	TotalInstances     int    `json:"total_instances"`     // 总实例数
	HealthyInstances   int    `json:"healthy_instances"`   // 健康实例数
	UnhealthyInstances int    `json:"unhealthy_instances"` // 不健康实例数
	OverallStatus      string `json:"overall_status"`      // 整体状态
	LastCheck          string `json:"last_check"`          // 最后检查时间
}

// New 创建新的处理器
func New(cfg *config.Config) *Handler {
	return &Handler{
		config:            cfg,
		alertRepo:         repository.NewAlertRepository(),
		serviceHealthRepo: repository.NewServiceHealthRepository(),
		notificationRepo:  repository.NewNotificationRepository(),
		smsNotifier:       service.NewSMSNotifier(cfg),
	}
}

// SetupRoutes 设置Gin路由
func (h *Handler) SetupRoutes() *gin.Engine {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(gin.Recovery())        // 恢复中间件
	r.Use(h.loggingMiddleware()) // 自定义日志中间件
	r.Use(h.corsMiddleware())    // CORS中间件

	// 基础路由
	r.GET("/health", h.healthCheck)
	r.GET("/info", h.systemInfo)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 告警相关路由
		alerts := api.Group("/alerts")
		{
			alerts.GET("", h.getAlerts)
			alerts.GET("/:id", h.getAlert)
			alerts.POST("/:id/resolve", h.resolveAlert)
			alerts.GET("/statistics", h.getAlertStatistics)
		}

		// 服务健康相关路由
		services := api.Group("/services")
		{
			services.GET("", h.getServices)
			services.GET("/:name", h.getService)
			services.GET("/:name/instances", h.getServiceInstances)
			services.GET("/:name/instances/:instanceId", h.getServiceInstance)
			services.GET("/statistics", h.getServiceStatistics)
		}

		// Agent管理相关路由
		agents := api.Group("/agents")
		{
			agents.GET("", h.getAgents)
			agents.GET("/:host/status", h.getAgentStatus)
			agents.POST("/:host/containers/:name/restart", h.restartContainerViaAgent)
			agents.GET("/:host/system/info", h.getAgentSystemInfo)
			agents.GET("/:host/system/resources", h.getAgentSystemResources)
			agents.GET("/:host/system/disk-usage", h.getAgentDiskUsage)
		}

		// 通知相关路由
		notifications := api.Group("/notifications")
		{
			notifications.GET("", h.getNotifications)
			notifications.GET("/:id", h.getNotification)
			notifications.GET("/alert/:alertId", h.getNotificationsByAlert)
			notifications.GET("/statistics", h.getNotificationStatistics)
		}

		// 短信测试相关路由
		sms := api.Group("/sms")
		{
			sms.POST("/test", h.testSMS)
		}
	}

	return r
}

// healthCheck 健康检查处理器
func (h *Handler) healthCheck(c *gin.Context) {
	response := gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"service":   "monitor",
		"version":   "1.0.0",
	}

	c.JSON(http.StatusOK, response)
}

// systemInfo 系统信息处理器
func (h *Handler) systemInfo(c *gin.Context) {
	response := gin.H{
		"service":     "monitor",
		"version":     "1.0.0",
		"description": "Monitoring Platform",
		"timestamp":   time.Now().Format(time.RFC3339),
		"config": gin.H{
			"server_port": h.config.Server.Port,
			"log_level":   h.config.Logging.Level,
		},
	}

	c.JSON(http.StatusOK, response)
}

// getAlerts 获取告警列表
func (h *Handler) getAlerts(c *gin.Context) {
	// 获取查询参数
	status := c.Query("status")
	serviceName := c.Query("service")

	var alerts []model.Alert
	var err error

	if status != "" {
		alerts, err = h.alertRepo.GetAlertsByStatus(model.AlertStatus(status))
	} else if serviceName != "" {
		alerts, err = h.alertRepo.GetAlertsByService(serviceName)
	} else {
		alerts, err = h.alertRepo.GetActiveAlerts()
	}

	if err != nil {
		logger.Error("获取告警失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取告警失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  alerts,
		"count": len(alerts),
	})
}

// getAlert 获取单个告警详情
func (h *Handler) getAlert(c *gin.Context) {
	id := c.Param("id")

	// 这里需要将string转换为uint，简化处理
	var alertID uint
	if _, err := fmt.Sscanf(id, "%d", &alertID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的告警ID"})
		return
	}

	alert, err := h.alertRepo.GetByID(alertID)
	if err != nil {
		logger.Error("获取告警失败", "error", err, "id", alertID)
		c.JSON(http.StatusNotFound, gin.H{"error": "告警未找到"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": alert})
}

// resolveAlert 解决告警
func (h *Handler) resolveAlert(c *gin.Context) {
	id := c.Param("id")

	var alertID uint
	if _, err := fmt.Sscanf(id, "%d", &alertID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid alert ID"})
		return
	}

	if err := h.alertRepo.ResolveAlert(alertID); err != nil {
		logger.Error("Failed to resolve alert", "error", err, "id", alertID)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to resolve alert"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Alert resolved successfully"})
}

// getAlertStatistics 获取告警统计信息
func (h *Handler) getAlertStatistics(c *gin.Context) {
	stats, err := h.alertRepo.GetAlertStatistics()
	if err != nil {
		logger.Error("Failed to get alert statistics", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get statistics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// getServices 获取服务列表
func (h *Handler) getServices(c *gin.Context) {
	status := c.Query("status")
	includeInstances := c.Query("include_instances") == "true"

	if includeInstances {
		// 返回包含实例信息的服务列表
		h.getServicesWithInstances(c, status)
		return
	}

	// 传统模式：返回服务级别信息（向后兼容）
	var services []model.ServiceHealth
	var err error

	if status != "" {
		services, err = h.serviceHealthRepo.GetByStatus(model.HealthStatus(status))
	} else {
		services, err = h.serviceHealthRepo.GetAll()
	}

	if err != nil {
		logger.Error("获取服务失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取服务失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  services,
		"count": len(services),
	})
}

// getServicesWithInstances 获取包含实例信息的服务列表
func (h *Handler) getServicesWithInstances(c *gin.Context, status string) {
	// 获取所有实例
	var allInstances []model.ServiceHealth
	var err error

	if status != "" {
		allInstances, err = h.serviceHealthRepo.GetInstancesByStatus(model.HealthStatus(status))
	} else {
		allInstances, err = h.serviceHealthRepo.GetAllInstances()
	}

	if err != nil {
		logger.Error("获取服务实例失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取服务实例失败"})
		return
	}

	// 按服务名称分组实例
	serviceMap := make(map[string][]model.ServiceHealth)
	for _, instance := range allInstances {
		serviceMap[instance.ServiceName] = append(serviceMap[instance.ServiceName], instance)
	}

	// 构建响应数据
	var servicesWithInstances []ServiceWithInstances
	for serviceName, instances := range serviceMap {
		summary := h.calculateServiceSummary(instances)
		servicesWithInstances = append(servicesWithInstances, ServiceWithInstances{
			ServiceName: serviceName,
			Instances:   instances,
			Summary:     summary,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  servicesWithInstances,
		"count": len(servicesWithInstances),
	})
}

// getService 获取单个服务详情
func (h *Handler) getService(c *gin.Context) {
	serviceName := c.Param("name")
	includeInstances := c.Query("include_instances") == "true"

	if includeInstances {
		// 返回包含所有实例的服务详情
		instances, err := h.serviceHealthRepo.GetInstancesByServiceName(serviceName)
		if err != nil {
			logger.Error("获取服务实例失败", "service", serviceName, "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取服务实例失败"})
			return
		}

		if len(instances) == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "服务不存在"})
			return
		}

		summary := h.calculateServiceSummary(instances)
		serviceWithInstances := ServiceWithInstances{
			ServiceName: serviceName,
			Instances:   instances,
			Summary:     summary,
		}

		c.JSON(http.StatusOK, gin.H{"data": serviceWithInstances})
		return
	}

	// 传统模式：返回单个服务信息（向后兼容）
	service, err := h.serviceHealthRepo.GetByServiceName(serviceName)
	if err != nil {
		logger.Error("Failed to get service", "error", err, "service", serviceName)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get service"})
		return
	}

	if service == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": service})
}

// getServiceInstances 获取服务的所有实例
func (h *Handler) getServiceInstances(c *gin.Context) {
	serviceName := c.Param("name")

	instances, err := h.serviceHealthRepo.GetInstancesByServiceName(serviceName)
	if err != nil {
		logger.Error("获取服务实例失败", "service", serviceName, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取服务实例失败"})
		return
	}

	if len(instances) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "服务不存在或没有实例"})
		return
	}

	summary := h.calculateServiceSummary(instances)

	c.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"service_name": serviceName,
			"instances":    instances,
			"summary":      summary,
		},
		"count": len(instances),
	})
}

// getServiceInstance 获取特定服务实例详情
func (h *Handler) getServiceInstance(c *gin.Context) {
	serviceName := c.Param("name")
	instanceID := c.Param("instanceId")

	instance, err := h.serviceHealthRepo.GetByServiceNameAndInstance(serviceName, instanceID)
	if err != nil {
		logger.Error("获取服务实例详情失败",
			"service", serviceName,
			"instance_id", instanceID,
			"error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取服务实例详情失败"})
		return
	}

	if instance == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "服务实例不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": instance})
}

// getServiceStatistics 获取服务统计信息
func (h *Handler) getServiceStatistics(c *gin.Context) {
	stats, err := h.serviceHealthRepo.GetServiceStatistics()
	if err != nil {
		logger.Error("Failed to get service statistics", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get statistics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// loggingMiddleware Gin日志中间件
func (h *Handler) loggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 记录请求日志
		duration := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		logger.Info("HTTP请求",
			"方法", method,
			"路径", path,
			"状态", statusCode,
			"持续时间", duration.String(),
			"客户端IP", clientIP,
			"用户代理", c.Request.UserAgent(),
		)
	}
}

// corsMiddleware CORS中间件
func (h *Handler) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// getAgents 获取所有Agent信息
func (h *Handler) getAgents(c *gin.Context) {
	// 从配置中获取所有Agent信息
	cfg := h.config
	if cfg == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Configuration not available",
		})
		return
	}

	var agents []gin.H
	agentMap := make(map[string]gin.H)

	// 遍历所有服务，收集Agent信息
	for _, service := range cfg.Services {
		if service.Docker != nil && service.Docker.Agent != nil {
			agentKey := fmt.Sprintf("%s:%d", service.Docker.Agent.Host, service.Docker.Agent.Port)

			if _, exists := agentMap[agentKey]; !exists {
				agentMap[agentKey] = gin.H{
					"host":     service.Docker.Agent.Host,
					"port":     service.Docker.Agent.Port,
					"services": []string{service.Name},
				}
			} else {
				agent := agentMap[agentKey]
				services := agent["services"].([]string)
				services = append(services, service.Name)
				agent["services"] = services
				agentMap[agentKey] = agent
			}
		}
	}

	// 转换为数组
	for _, agent := range agentMap {
		agents = append(agents, agent)
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  agents,
		"count": len(agents),
	})
}

// getAgentStatus 获取Agent状态
func (h *Handler) getAgentStatus(c *gin.Context) {
	host := c.Param("host")
	if host == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Host parameter is required",
		})
		return
	}

	// 查找对应的Agent配置
	cfg := h.config
	if cfg == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Configuration not available",
		})
		return
	}

	var agentConfig *config.AgentConfig
	for _, service := range cfg.Services {
		if service.Docker != nil && service.Docker.Agent != nil && service.Docker.Agent.Host == host {
			agentConfig = service.Docker.Agent
			break
		}
	}

	if agentConfig == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Agent not found",
		})
		return
	}

	// 调用Agent健康检查接口
	url := fmt.Sprintf("http://%s:%d/api/v1/health", agentConfig.Host, agentConfig.Port)
	resp, err := http.Get(url)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Agent unreachable",
			"details": err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":  "Agent unhealthy",
			"status": resp.StatusCode,
		})
		return
	}

	var healthData map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&healthData); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to parse agent response",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": healthData,
	})
}

// restartContainerViaAgent 通过Agent重启容器
func (h *Handler) restartContainerViaAgent(c *gin.Context) {
	host := c.Param("host")
	containerName := c.Param("name")

	if host == "" || containerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Host and container name are required",
		})
		return
	}

	// 查找对应的服务配置
	cfg := h.config
	if cfg == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Configuration not available",
		})
		return
	}

	var serviceConfig *config.ServiceConfig
	for _, service := range cfg.Services {
		if service.Docker != nil && service.Docker.Agent != nil &&
			service.Docker.Agent.Host == host && service.Docker.ContainerName == containerName {
			serviceConfig = &service
			break
		}
	}

	if serviceConfig == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Service configuration not found",
		})
		return
	}

	// 直接调用Agent API重启容器
	agentConfig := serviceConfig.Docker.Agent
	url := fmt.Sprintf("http://%s:%d/api/v1/docker/containers/%s/restart",
		agentConfig.Host, agentConfig.Port, containerName)

	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create request",
			"details": err.Error(),
		})
		return
	}

	req.Header.Set("Authorization", "Bearer "+agentConfig.Token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: time.Duration(agentConfig.Timeout) * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to call agent",
			"details": err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "Agent returned error",
			"status": resp.StatusCode,
		})
		return
	}

	logger.Info("Container restart initiated via agent",
		"host", host,
		"container", containerName)

	c.JSON(http.StatusOK, gin.H{
		"message":   "Container restart initiated successfully",
		"host":      host,
		"container": containerName,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// getAgentSystemInfo 获取Agent系统信息
func (h *Handler) getAgentSystemInfo(c *gin.Context) {
	host := c.Param("host")
	if host == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Host parameter is required",
		})
		return
	}

	// 查找Agent配置
	agentConfig := h.findAgentConfig(host)
	if agentConfig == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Agent not found",
		})
		return
	}

	// 调用Agent系统信息接口
	url := fmt.Sprintf("http://%s:%d/api/v1/system/info", agentConfig.Host, agentConfig.Port)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create request",
			"details": err.Error(),
		})
		return
	}

	req.Header.Set("Authorization", "Bearer "+agentConfig.Token)

	client := &http.Client{Timeout: time.Duration(agentConfig.Timeout) * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Agent不可达",
			"details": err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":  "Agent返回错误",
			"status": resp.StatusCode,
		})
		return
	}

	var systemInfo map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&systemInfo); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "解析Agent响应失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, systemInfo)
}

// getAgentSystemResources 获取Agent系统资源
func (h *Handler) getAgentSystemResources(c *gin.Context) {
	host := c.Param("host")
	if host == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Host parameter is required",
		})
		return
	}

	// 查找Agent配置
	agentConfig := h.findAgentConfig(host)
	if agentConfig == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Agent not found",
		})
		return
	}

	// 调用Agent系统资源接口
	url := fmt.Sprintf("http://%s:%d/api/v1/system/resources", agentConfig.Host, agentConfig.Port)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create request",
			"details": err.Error(),
		})
		return
	}

	req.Header.Set("Authorization", "Bearer "+agentConfig.Token)

	client := &http.Client{Timeout: time.Duration(agentConfig.Timeout) * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Agent不可达",
			"details": err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":  "Agent返回错误",
			"status": resp.StatusCode,
		})
		return
	}

	var resources map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&resources); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "解析Agent响应失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resources)
}

// getNotifications 获取通知列表
func (h *Handler) getNotifications(c *gin.Context) {
	// 获取查询参数
	status := c.Query("status")
	channel := c.Query("channel")
	recipient := c.Query("recipient")

	var notifications []model.Notification
	var err error

	if status != "" {
		notifications, err = h.notificationRepo.GetNotificationsByStatus(model.NotificationStatus(status))
	} else if channel != "" {
		notifications, err = h.notificationRepo.GetNotificationsByChannel(model.NotificationChannel(channel))
	} else if recipient != "" {
		notifications, err = h.notificationRepo.GetNotificationsByRecipient(recipient)
	} else {
		// 获取最近的通知记录
		notifications, err = h.notificationRepo.GetNotificationsInTimeRange(
			time.Now().AddDate(0, 0, -7), // 最近7天
			time.Now(),
		)
	}

	if err != nil {
		logger.Error("Failed to get notifications", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notifications"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  notifications,
		"count": len(notifications),
	})
}

// getNotification 获取单个通知详情
func (h *Handler) getNotification(c *gin.Context) {
	id := c.Param("id")

	var notificationID uint
	if _, err := fmt.Sscanf(id, "%d", &notificationID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的通知ID"})
		return
	}

	notification, err := h.notificationRepo.GetByID(notificationID)
	if err != nil {
		logger.Error("获取通知失败", "error", err, "id", notificationID)
		c.JSON(http.StatusNotFound, gin.H{"error": "通知未找到"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": notification})
}

// getNotificationsByAlert 获取告警相关的通知
func (h *Handler) getNotificationsByAlert(c *gin.Context) {
	alertId := c.Param("alertId")

	var alertID uint
	if _, err := fmt.Sscanf(alertId, "%d", &alertID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid alert ID"})
		return
	}

	notifications, err := h.notificationRepo.GetByAlertID(alertID)
	if err != nil {
		logger.Error("Failed to get notifications by alert", "error", err, "alert_id", alertID)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notifications"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  notifications,
		"count": len(notifications),
	})
}

// getNotificationStatistics 获取通知统计信息
func (h *Handler) getNotificationStatistics(c *gin.Context) {
	stats, err := h.notificationRepo.GetNotificationStats()
	if err != nil {
		logger.Error("Failed to get notification statistics", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get statistics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// getAgentDiskUsage 获取Agent磁盘使用情况分析
func (h *Handler) getAgentDiskUsage(c *gin.Context) {
	host := c.Param("host")
	if host == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Host parameter is required",
		})
		return
	}

	// 查找Agent配置
	agentConfig := h.findAgentConfig(host)
	if agentConfig == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Agent not found",
		})
		return
	}

	// 构建请求URL，传递查询参数
	url := fmt.Sprintf("http://%s:%d/api/v1/system/disk-usage", agentConfig.Host, agentConfig.Port)

	// 添加查询参数
	queryParams := c.Request.URL.Query()
	if len(queryParams) > 0 {
		url += "?" + queryParams.Encode()
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create request",
			"details": err.Error(),
		})
		return
	}

	req.Header.Set("Authorization", "Bearer "+agentConfig.Token)

	client := &http.Client{Timeout: time.Duration(agentConfig.Timeout) * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Agent unreachable",
			"details": err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":  "Agent returned error",
			"status": resp.StatusCode,
		})
		return
	}

	var diskUsage map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&diskUsage); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to parse agent response",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, diskUsage)
}

// findAgentConfig 查找Agent配置的辅助方法
func (h *Handler) findAgentConfig(host string) *config.AgentConfig {
	cfg := h.config
	if cfg == nil {
		return nil
	}

	for _, service := range cfg.Services {
		if service.Docker != nil && service.Docker.Agent != nil && service.Docker.Agent.Host == host {
			return service.Docker.Agent
		}
	}
	return nil
}

// testSMS 短信发送测试接口
func (h *Handler) testSMS(c *gin.Context) {
	logger.Info("收到短信测试请求", "remote_addr", c.ClientIP())

	// 检查短信配置是否存在
	if h.config.Notifications.Channels.SMS == nil {
		logger.Error("短信配置未找到")
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success":   false,
			"message":   "短信服务未配置",
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	// 解析请求体
	var req SMSTestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("解析短信测试请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success":   false,
			"message":   fmt.Sprintf("请求参数错误: %v", err),
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	// 验证手机号码格式（简单验证）
	if len(req.Phone) < 11 {
		logger.Error("手机号码格式错误", "phone", req.Phone)
		c.JSON(http.StatusBadRequest, gin.H{
			"success":   false,
			"message":   "手机号码格式错误",
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	// 验证短信内容长度
	if len(req.Content) == 0 || len(req.Content) > 500 {
		logger.Error("短信内容长度错误", "content_length", len(req.Content))
		c.JSON(http.StatusBadRequest, gin.H{
			"success":   false,
			"message":   "短信内容长度必须在1-500字符之间",
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	// 构建短信发送请求
	smsConfig := h.config.Notifications.Channels.SMS
	smsReq := &service.SMSSendRequest{
		Account:  smsConfig.Username,
		Password: smsConfig.Password,
		Phone:    req.Phone,
		Content:  req.Content,
		SendTime: "", // 立即发送
	}

	logger.Info("开始发送测试短信",
		"phone", req.Phone,
		"content_length", len(req.Content))

	// 发送短信
	response, err := h.smsNotifier.SendSMS(smsReq)
	timestamp := time.Now().Format(time.RFC3339)

	if err != nil {
		logger.Error("短信发送失败", "error", err, "phone", req.Phone)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":   false,
			"message":   fmt.Sprintf("短信发送失败: %v", err),
			"timestamp": timestamp,
		})
		return
	}

	// 检查响应状态
	if !h.smsNotifier.IsSuccessResponse(response) {
		errorMsg := h.smsNotifier.GetErrorMessage(response)
		logger.Error("短信发送失败", "error_msg", errorMsg, "response_code", response.Response)
		c.JSON(http.StatusBadRequest, gin.H{
			"success":   false,
			"message":   fmt.Sprintf("短信发送失败: %s", errorMsg),
			"timestamp": timestamp,
		})
		return
	}

	// 获取短信ID
	smsID := ""
	if response.SMS != nil {
		smsID = response.SMS.SMSID
	}

	logger.Info("短信发送成功",
		"phone", req.Phone,
		"sms_id", smsID,
		"response_code", response.Response)

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "短信发送成功",
		"sms_id":    smsID,
		"timestamp": timestamp,
	})
}

// calculateServiceSummary 计算服务汇总信息
func (h *Handler) calculateServiceSummary(instances []model.ServiceHealth) ServiceSummary {
	totalInstances := len(instances)
	healthyInstances := 0
	unhealthyInstances := 0
	var lastCheck time.Time

	for _, instance := range instances {
		if instance.Status == model.HealthStatusHealthy {
			healthyInstances++
		} else {
			unhealthyInstances++
		}

		// 找到最新的检查时间
		if instance.LastCheck.After(lastCheck) {
			lastCheck = instance.LastCheck
		}
	}

	// 确定整体状态
	var overallStatus string
	if healthyInstances == totalInstances {
		overallStatus = "healthy"
	} else if healthyInstances == 0 {
		overallStatus = "unhealthy"
	} else {
		overallStatus = "degraded"
	}

	return ServiceSummary{
		TotalInstances:     totalInstances,
		HealthyInstances:   healthyInstances,
		UnhealthyInstances: unhealthyInstances,
		OverallStatus:      overallStatus,
		LastCheck:          lastCheck.Format("2006-01-02 15:04:05"),
	}
}
