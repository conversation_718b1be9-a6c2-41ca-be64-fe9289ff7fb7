package logger

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"strings"
	"time"
)

var defaultLogger *slog.Logger

// getLocalTimeZone 获取本地时区，优先使用中国时区
func getLocalTimeZone() *time.Location {
	// 首先尝试从环境变量TZ获取时区
	if tz := os.Getenv("TZ"); tz != "" {
		if loc, err := time.LoadLocation(tz); err == nil {
			return loc
		}
	}

	// 尝试加载中国时区
	if loc, err := time.LoadLocation("Asia/Shanghai"); err == nil {
		return loc
	}

	// 如果都失败了，使用系统本地时区
	return time.Local
}

// Init 初始化日志系统
func Init() error {
	return InitWithFormat("console") // 默认使用控制台格式
}

// InitWithFormat 使用指定格式初始化日志系统
func InitWithFormat(format string) error {
	var handler slog.Handler

	options := &slog.HandlerOptions{
		Level:     slog.LevelInfo,
		AddSource: true,
	}

	// 根据格式选择处理器
	switch strings.ToLower(format) {
	case "json":
		handler = slog.NewJSONHandler(os.Stdout, options)
	case "console":
		handler = NewConsoleHandler(os.Stdout, options)
	case "text", "":
		handler = slog.NewTextHandler(os.Stdout, options)
	default:
		handler = NewConsoleHandler(os.Stdout, options)
	}

	defaultLogger = slog.New(handler)
	slog.SetDefault(defaultLogger)

	return nil
}

// Info 记录信息级别日志
func Info(msg string, args ...any) {
	defaultLogger.Info(msg, args...)
}

// Error 记录错误级别日志
func Error(msg string, args ...any) {
	defaultLogger.Error(msg, args...)
}

// Warn 记录警告级别日志
func Warn(msg string, args ...any) {
	defaultLogger.Warn(msg, args...)
}

// Debug 记录调试级别日志
func Debug(msg string, args ...any) {
	defaultLogger.Debug(msg, args...)
}

// Fatal 记录致命错误并退出程序
func Fatal(msg string, args ...any) {
	defaultLogger.Error(msg, args...)
	os.Exit(1)
}

// With 创建带有额外字段的日志记录器
func With(args ...any) *slog.Logger {
	return defaultLogger.With(args...)
}

// ConsoleHandler 自定义控制台处理器
type ConsoleHandler struct {
	opts slog.HandlerOptions
	w    io.Writer
}

// NewConsoleHandler 创建新的控制台处理器
func NewConsoleHandler(w io.Writer, opts *slog.HandlerOptions) *ConsoleHandler {
	if opts == nil {
		opts = &slog.HandlerOptions{}
	}
	return &ConsoleHandler{
		opts: *opts,
		w:    w,
	}
}

// Enabled 检查日志级别是否启用
func (h *ConsoleHandler) Enabled(_ context.Context, level slog.Level) bool {
	minLevel := slog.LevelInfo
	if h.opts.Level != nil {
		minLevel = h.opts.Level.Level()
	}
	return level >= minLevel
}

// Handle 处理日志记录
func (h *ConsoleHandler) Handle(_ context.Context, r slog.Record) error {
	// 格式化时间 - 转换为本地时区
	localTime := r.Time.In(getLocalTimeZone())
	timeStr := localTime.Format("2006-01-02 15:04:05")

	// 选择级别颜色
	var levelStr string
	switch r.Level {
	case slog.LevelDebug:
		levelStr = "\033[36mDEBUG\033[0m" // 青色
	case slog.LevelInfo:
		levelStr = "\033[32mINFO\033[0m" // 绿色
	case slog.LevelWarn:
		levelStr = "\033[33mWARN\033[0m" // 黄色
	case slog.LevelError:
		levelStr = "\033[31mERROR\033[0m" // 红色
	default:
		levelStr = r.Level.String()
	}

	// 构建基础消息
	msg := fmt.Sprintf("%s [%s] %s", timeStr, levelStr, r.Message)

	// 添加属性
	if r.NumAttrs() > 0 {
		var attrs []string
		r.Attrs(func(a slog.Attr) bool {
			attrs = append(attrs, fmt.Sprintf("%s=%v", a.Key, a.Value))
			return true
		})
		if len(attrs) > 0 {
			msg += " " + strings.Join(attrs, " ")
		}
	}

	// 添加源码信息（如果启用）
	if h.opts.AddSource && r.PC != 0 {
		// 这里可以添加源码位置信息，但为了简洁暂时省略
	}

	fmt.Fprintln(h.w, msg)
	return nil
}

// WithAttrs 添加属性
func (h *ConsoleHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	// 简化实现，返回自身
	return h
}

// WithGroup 添加组
func (h *ConsoleHandler) WithGroup(name string) slog.Handler {
	// 简化实现，返回自身
	return h
}
