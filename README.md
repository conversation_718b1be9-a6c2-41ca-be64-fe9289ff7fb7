# Monitor System

一个基于Go语言开发的分布式服务监控系统，支持HTTP服务、Kafka集群、EMQX/MQTT等多种监控类型，具备自动服务发现、多实例监控、智能告警和容器管理等功能。

## 主要特性

### 🚀 核心功能
- **多类型监控**: 支持HTTP、Kafka、EMQX/MQTT、业务API等监控类型
- **服务发现**: 集成Nacos服务发现，自动发现和监控服务实例
- **多实例监控**: 支持对同一服务的多个实例进行独立监控
- **智能告警**: 支持多种告警规则和通知渠道
- **容器管理**: 集成Docker容器自动重启功能
- **实时监控**: 提供实时的服务健康状态和性能指标

### 🔧 技术特性
- **高可用**: 支持分布式部署和故障转移
- **可扩展**: 模块化设计，易于扩展新的监控类型
- **向后兼容**: 新功能默认关闭，保持向后兼容性
- **配置热更新**: 支持配置的动态更新
- **丰富的API**: 提供完整的REST API接口

## 快速开始

### 环境要求

- Go 1.19+
- PostgreSQL 12+
- Nacos 1.4.0+
- Docker (可选，用于容器管理)

### 安装部署

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd monitor
   ```

2. **编译项目**
   ```bash
   # 编译服务端
   go build -o bin/monitor-server cmd/server/main.go
   
   # 编译Agent端
   go build -o bin/monitor-agent cmd/agent/main.go
   ```

3. **配置数据库**
   ```bash
   # 执行数据库初始化脚本
   psql -U monitor -d monitor -f scripts/init.sql
   
   # 如果需要多实例监控，执行迁移脚本
   psql -U monitor -d monitor -f scripts/migrate_multi_instance.sql
   ```

4. **配置Nacos**
   ```bash
   # 复制配置模板
   cp configs/server/nacos-example.yml configs/server/nacos.yml
   
   # 编辑Nacos连接配置
   vim configs/server/nacos.yml
   ```

5. **启动服务**
   ```bash
   # 启动Monitor服务端
   ./bin/monitor-server
   
   # 启动Agent（如果需要容器管理功能）
   ./bin/monitor-agent
   ```

### 基础配置示例

```yaml
monitor:
  # 系统默认启用服务发现和多实例监控
  
  # 服务监控配置
  services:
    # HTTP服务监控
    - name: "user-service"
      type: "http"
      endpoint: "http://***********00:8080/health"
      check_interval: 30
      timeout: 10
      docker:
        container_name: "user-service"
    
    # 多实例HTTP服务监控（默认启用）
    - name: "api-gateway"
      check_interval: 20
      timeout: 8
      docker:
        container_name: "api-gateway"
        agent:
          host: "***********05"
```

## 多实例监控

### 功能概述

多实例监控是Monitor系统的核心功能之一，允许对Nacos中注册的同一服务的多个实例进行独立监控。

### 主要特性

- **自动实例发现**: 从Nacos自动获取服务的所有健康实例
- **独立监控**: 每个实例独立进行健康检查和状态管理
- **实例级别数据存储**: 支持按实例存储健康状态和监控数据
- **实例级别API**: 提供查询特定实例状态的API接口
- **默认启用**: 所有HTTP服务默认启用多实例监控

### 快速启用

1. **自动服务发现**
   系统默认启用服务发现功能，自动从Nacos发现所有HTTP服务

2. **配置服务**
   ```yaml
   services:
     - name: "user-service"
       check_interval: 30
       docker:
         container_name: "user-service"
   ```

3. **查看监控状态**
   ```bash
   # 获取服务所有实例状态
   curl http://localhost:8080/api/v1/services/user-service/instances
   
   # 获取特定实例状态
   curl http://localhost:8080/api/v1/services/user-service/instances/***********:8080
   ```

详细使用说明请参考：[多实例监控使用指南](docs/多实例监控使用指南.md)

## API接口

### 服务监控API

```bash
# 获取所有服务状态
GET /api/v1/services

# 获取特定服务状态
GET /api/v1/services/{serviceName}

# 获取服务所有实例状态（多实例监控）
GET /api/v1/services/{serviceName}/instances

# 获取特定实例状态（多实例监控）
GET /api/v1/services/{serviceName}/instances/{instanceId}
```

### 告警管理API

```bash
# 获取告警列表
GET /api/v1/alerts

# 获取特定告警详情
GET /api/v1/alerts/{alertId}
```

## 监控类型

### HTTP服务监控
- 支持GET、POST、PUT、DELETE等HTTP方法
- 可配置期望的HTTP状态码
- 支持自定义健康检查路径
- 支持基本认证

### Kafka集群监控
- 监控消费者组延迟
- 支持多broker集群
- 支持SASL认证和TLS加密
- 主题和分区级别的监控

### EMQX/MQTT监控
- 监控MQTT消息速率
- 支持多主题监控
- 基线速率计算和异常检测
- 支持MQTT认证

## 告警和通知

### 支持的通知渠道
- **邮件通知**: 支持SMTP和HTTP接口两种方式
- **短信通知**: 支持HTTP短信接口
- **Webhook**: 支持自定义Webhook通知

### 告警规则类型
- 连续失败告警
- 响应时间告警
- 资源使用率告警
- Kafka延迟告警
- EMQX速率下降告警

### 告警升级策略
- 多级告警升级
- 时间窗口控制
- 告警抑制规则
- 自动恢复机制

## 容器管理

### Docker集成
- 支持容器自动重启
- 可配置重启策略和冷却时间
- 支持远程Docker管理
- 集成Agent代理架构

### Agent架构
- 分布式Agent部署
- 统一的容器管理接口
- 安全的Token认证
- 故障转移支持

## 配置管理

### 配置中心集成
- 集成Nacos配置中心
- 支持配置热更新
- 配置版本管理
- 环境隔离

### 配置验证
- 启动时配置验证
- 运行时配置检查
- 详细的错误提示
- 配置兼容性检查

## 开发和测试

### 运行测试

```bash
# 运行所有测试
go test ./...

# 运行特定模块测试
go test ./internal/server/config
go test ./internal/server/service
go test ./internal/server/handler

# 运行集成测试
go test ./internal/server -run Integration
```

### 测试覆盖率

```bash
# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

## 文档

- [多实例监控配置指南](docs/multi-instance-monitoring.md)
- [多实例监控使用指南](docs/多实例监控使用指南.md)
- [短信接口文档](docs/短信接口文档.md)
- [邮箱接口文档](docs/邮箱接口文档.mail)

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

本项目采用MIT许可证。
