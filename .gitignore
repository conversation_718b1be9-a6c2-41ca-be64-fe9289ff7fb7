### Go ###
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Go workspace file
go.work

### IDE ###
# IntelliJ project files
.idea
*.iml
out
gen
.fastRequest

# VSCode
.vscode/

### OS ###
# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db

### Docker ###
# Docker volumes
postgres_data/

### Logs ###
*.log
logs/
log/

### Environment ###
.env
.env.local
.env.*.local

### Temporary files ###
tmp/
temp/

### Cache ###
cache/
*.cache

### Backup files ###
*.backup
*.bak
*.orig
go.mod.backup

### Build output ###
bin/
build/
dist/

### Nacos cache ###
nacos/
nacos-logs/